// useDataDogRUM.ts
import { datadogRum } from '@datadog/browser-rum';
import { onMounted, onUnmounted } from 'vue';

// Configuration interface for DataDog RUM
interface DataDogConfig {
    applicationId: string;
    clientToken: string;
    site?: string;
    service: string;
    env: string;
    version?: string;
    sessionSampleRate?: number;
    sessionReplaySampleRate?: number;
    trackUserInteractions?: boolean;
    trackResources?: boolean;
    trackLongTasks?: boolean;
    defaultPrivacyLevel?: 'allow' | 'mask' | 'mask-user-input';
}

export function useDataDogRUM() {
    let isInitialized = false;
    let isDisabled = false;

    // Initialize DataDog RUM
    const init = (config: DataDogConfig) => {
        if (isDisabled) {
            console.warn('DataDog RUM is disabled.');
            return;
        }
        if (isInitialized) {
            console.warn('DataDog RUM is already initialized.');
            return;
        }

        // default to datadoghq.com if no site is provided
        const site = config.site || 'datadoghq.com';
        // console.log('DataDog RUM config', config);

        try {
            datadogRum.init({
                applicationId: config.applicationId,
                clientToken: config.clientToken,
                site: site as any,
                service: config.service,
                env: config.env,
                version: config.version || '1.0.0',
                sessionSampleRate: config.sessionSampleRate ?? 100,
                sessionReplaySampleRate: config.sessionReplaySampleRate ?? 20,
                trackUserInteractions: config.trackUserInteractions ?? true,
                trackResources: config.trackResources ?? true,
                trackLongTasks: config.trackLongTasks ?? true,
                defaultPrivacyLevel: config.defaultPrivacyLevel ?? 'mask',
            });

            if (config.sessionReplaySampleRate && config.sessionReplaySampleRate > 0) {
                datadogRum.startSessionReplayRecording();
            }

            isInitialized = true;
            console.log('DataDog RUM initialized successfully.');
        } catch (error) {
            console.error('Failed to initialize DataDog RUM:', error);
        }
    };

    // Set user information
    const setUser = (user: { id: string; name?: string; email?: string }) => {
        if (isDisabled) {
            return;
        }
        if (!isInitialized) {
            console.warn('DataDog RUM is not initialized. Cannot set user.');
            return;
        }
        datadogRum.setUser(user);
    };

    // Track a custom event
    const addAction = (name: string, context?: Record<string, any>) => {
        if (isDisabled) {
            return;
        }
        if (!isInitialized) {
            console.warn('DataDog RUM is not initialized. Cannot add action.');
            return;
        }
        datadogRum.addAction(name, context);
    };

    // Track a custom error
    const addError = (error: Error, context?: Record<string, any>) => {
        if (isDisabled) {
            return;
        }
        if (!isInitialized) {
            console.warn('DataDog RUM is not initialized. Cannot add error.');
            return;
        }
        datadogRum.addError(error, context);
    };

    // Track a page view
    const trackPageView = (path: string, name?: string) => {
        if (isDisabled) {
            return;
        }
        if (!isInitialized) {
            console.warn('DataDog RUM is not initialized. Cannot track page view.');
            return;
        }
        datadogRum.startView({
            name: name || path,
            service: datadogRum.getInitConfiguration()?.service,
            context: {
                route: { path, name },
            }
        });
    };

    const disable = () => {
        isDisabled = true;
    };

    const enable = () => {
        isDisabled = false;
    };

    onMounted(() => {
        // Optional: Initialize here if config is provided in component
    });

    onUnmounted(() => {
        isInitialized = false;
    });

    return {
        init,
        setUser,
        addAction,
        addError,
        trackPageView,
        isInitialized,
        disable,
        enable,
    };
}
