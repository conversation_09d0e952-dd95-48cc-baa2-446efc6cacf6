import { defineStore } from 'pinia';
import { ref } from 'vue';
import { partnerAPI } from '@/services/PartnerAPI';
import type { Product } from '@/types/partner';

export const usePartnerStore = defineStore('partner', () => {
    const products = ref<Product[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);

    async function fetchProducts() {
        try {
            loading.value = true;
            error.value = null;
            
            const response = await partnerAPI.fetchProducts();
            if (response.success) {
                products.value = response.partners_product_list.results;
            } else {
                throw new Error('Failed to fetch products');
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch products';
            console.error('Error fetching products:', err);
        } finally {
            loading.value = false;
        }
    }

    return {
        products,
        loading,
        error,
        fetchProducts
    };
}); 