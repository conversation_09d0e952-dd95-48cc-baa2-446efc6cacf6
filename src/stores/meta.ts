import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useMetaAPI } from '@/composables/services/useMetaAPI'

export const useMetaStore = defineStore('meta', () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastRefreshTime = ref<Date | null>(null)
  const metaData = ref<any>(null)
  const partnerMetaData = ref<any>(null)

  async function loadMetaData() {
    try {
      isLoading.value = true
      error.value = null
      
      const metaAPI = useMetaAPI()
      const response = await metaAPI.fetchMetaData()
      
      metaData.value = response
      lastRefreshTime.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load meta data'
      console.error('Error loading meta data:', err)
    } finally {
      isLoading.value = false
    }
  }

  async function loadPartnerMetaData() {
    try {
      isLoading.value = true
      error.value = null
      
      const metaAPI = useMetaAPI()
      const response = await metaAPI.fetchPartnerMetaData()
      
      partnerMetaData.value = response
      lastRefreshTime.value = new Date()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load partner meta data'
      console.error('Error loading partner meta data:', err)
    } finally {
      isLoading.value = false
    }
  }

  function getPartnerLabelById(partnerId: string): string {
    if (!partnerMetaData.value || !partnerMetaData.value.pl__partners_users) {
        return '';
    }

    const partner = partnerMetaData.value.pl__partners_users.find((partner: any) => partner.id === partnerId);

    return partner ? partner.lbl || '' : '';
  }

  async function reload() {
    return Promise.all([loadMetaData(), loadPartnerMetaData()])
  }

  return {
    isLoading,
    error,
    lastRefreshTime,
    metaData,
    partnerMetaData,
    loadMetaData,
    loadPartnerMetaData,
    getPartnerLabelById,
    reload
  }
});
