import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Issue, FetchCasesParams } from '../services/IssuesAPI';
import { useIssuesAPI, useSettingsAPI, useInteractionEventsAPI } from '@/composables/services';
import type { View } from '@/composables/services/useSettingsAPI';
import type { InteractionEvent, FetchInteractionEventsParams } from '@/composables/services/useInteractionEventsAPI';

export const useCasesStore = defineStore('cases', () => {
  const currentIssue = ref<Issue | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const issues = ref<Issue[]>([]);
  const views = ref<View[]>([]);
  const interactionEvents = ref<InteractionEvent[]>([]);
  const loadingEvents = ref(false);
  const eventsError = ref<string | null>(null);

  // Get issue ID from URL or use default
  const issueId = computed(() => {
    const params = new URLSearchParams(window.location.search);
    return params.get('issueId');
  });

  async function fetchCurrentIssue(issueId: string) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      currentIssue.value = await issuesAPI.fetchIssue(issueId);
      console.log('Cases Store: Current issue set to:', currentIssue.value);
    } catch (err) {
      console.error('Cases Store: Error in fetchCurrentIssue:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchViews() {
    loading.value = true;
    error.value = null;

    try {
      const settingsAPI = useSettingsAPI();
      views.value = await settingsAPI.fetchViews();
      console.log('Cases Store: Views set to:', views.value);
    } catch (err) {
      console.error('Cases Store: Error in fetchViews:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchCases(params: FetchCasesParams) {
    loading.value = true;
    error.value = null;

    try {
      const issuesAPI = useIssuesAPI();
      issues.value = await issuesAPI.fetchCases(params);
      console.log('Cases Store: Issues set to:', issues.value);
    } catch (err) {
      console.error('Cases Store: Error in fetchCases:', err);
      error.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchInteractionEvents(params: FetchInteractionEventsParams) {
    loadingEvents.value = true;
    eventsError.value = null;

    try {
      const filters = [{"property":"source_object_id","value":params.issueId},{"property":"source_object","value":"issues"}];
      params.filter = filters;
      const interactionEventsAPI = useInteractionEventsAPI();
      interactionEvents.value = await interactionEventsAPI.fetchInteractionEvents(params);
      console.log('Cases Store: Interaction events set to:', interactionEvents.value);
    } catch (err) {
      console.error('Cases Store: Error in fetchInteractionEvents:', err);
      eventsError.value = err instanceof Error ? err.message : 'An error occurred';
      throw err;
    } finally {
      loadingEvents.value = false;
    }
  }
  
  // Convenience method to fetch events for the current issue
  async function fetchCurrentIssueEvents(additionalParams: Partial<FetchInteractionEventsParams> = {}) {
    if (!currentIssue.value) {
      console.warn('Cannot fetch events: No current issue set');
      return;
    }
    
    return fetchInteractionEvents({
      issueId: currentIssue.value.id,
      ...additionalParams
    });
  }
  
  return {
    currentIssue,
    issues,
    loading,
    error,
    issueId,
    views,
    interactionEvents,
    loadingEvents,
    eventsError,
    fetchCurrentIssue,
    fetchViews,
    fetchCases,
    fetchInteractionEvents,
    fetchCurrentIssueEvents
  };
}); 