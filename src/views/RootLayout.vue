<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useUserStore } from '../stores/user'
import BravoMainNavigation from '@services/ui-component-library/components/BravoMainNavigation.vue'
import { useI18n } from 'vue-i18n'
import LogoSvg from '../assets/logo.svg'
import ProfileMenu from '../components/ProfileMenu.vue'
import AnnouncekitWidget from '../components/AnnouncekitWidget.vue'
import { usePermissions } from '@/composables/usePermissions'

// Define navigation item interface
interface NavItem {
  id: string;
  icon?: string;
  label: string;
  active: boolean;
}

const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()
const { t } = useI18n()
const profileMenu = ref()

// Initialize the permissions composable
const { can } = usePermissions()

const handleLogout = async () => {
  try {
    console.log('Logging out...')
    await authStore.logout()
    // Force immediate navigation to login page
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    // Still try to redirect to login on failure
    router.push('/login')
  }
}

onMounted(async () => {
  console.log('RootLayout mounted')
})

// Use a special icon class name that you can style
const navigationItems = ref<any[]>([
  {
    id: 'ovation',
    icon: 'custom-ovation-logo',  // This can be styled with CSS
    label: 'Ovation',
    active: false,
    isLogo: true
  },
  {
    id: 'inbox',
    icon: 'pi pi-inbox',
    label: 'Inbox',
    active: false,
    tooltip: t('nav.inbox')
  },
  {
    id: 'journeys',
    icon: 'pi pi-map',
    label: 'Journeys',
    active: false,
    tooltip: t('nav.journeys')
  },
  {
    id: 'knowledge',
    icon: 'pi pi-book',
    label: 'Knowledge',
    active: false,
    tooltip: t('nav.knowledge')
  }
])

const bottomItems = ref<any[]>([
  {
    id: 'settings',
    icon: 'pi pi-cog',
    label: 'Settings',
    active: false,
    tooltip: t('nav.settings')
  },
  {
    id: 'profile',
    label: 'Profile',
    active: false,
    tooltip: t('nav.profile'),
    avatar: {
      image: 'https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png',
      shape: 'circle'
    }
  },
])

// Create a computed property for navigation items that filters based on permissions
// FIXME refactor and delete me - see https://boomtown.atlassian.net/browse/CXM-81?focusedCommentId=57676
const filteredNavigationItems = computed(() => {
  return navigationItems.value.filter(item => {
    // Always show non-knowledge items
    if (item.id !== 'knowledge') return true;
    // Only show knowledge item if user has viewArticle permission
    return can.viewArticle();
  });
});

// Update active states based on current route
const updateActiveStates = () => {
  const currentPath = router.currentRoute.value.path
  navigationItems.value = navigationItems.value.map((item) => ({
    ...item,
    active: currentPath.startsWith(`/${item.id}`) && item.id !== 'ovation'
  }))
  bottomItems.value = bottomItems.value.map((item) => ({
    ...item,
    active: currentPath.startsWith(`/${item.id}`)
  }))
}

// Watch for route changes
watch(() => router.currentRoute.value.path, updateActiveStates, { immediate: true })

// Use any type for the handler param
const handleNavClick = (item: any) => {
  // Get the mouse event
  const event = window.event as MouseEvent
  
  if (item.id === 'profile' && profileMenu.value) {
    // Show profile menu when profile avatar is clicked
    profileMenu.value.toggle(event)
  } else if (item.id === 'ovation' || item.isLogo) {
    // Logo item - trigger animation when clicked
    // Find and animate the logo element
    const logoElement = document.querySelector('.ovation-logo')
    if (logoElement) {
      // Remove the class first to reset animation
      logoElement.classList.remove('spin-animation')
      // Force a reflow to restart animation
      void (logoElement as HTMLElement).offsetWidth
      // Add the class back to trigger animation
      logoElement.classList.add('spin-animation')
    }
    
    // Also try to animate the icon if it's rendered differently
    const iconElement = document.querySelector('.custom-ovation-logo')
    if (iconElement) {
      iconElement.classList.remove('spin-animation')
      void (iconElement as HTMLElement).offsetWidth
      iconElement.classList.add('spin-animation')
    }
  } else {
    // Check if Ctrl key was pressed during the click
    if (event.ctrlKey) {
      // Open in a new tab
      window.open(`/${item.id}`, '_blank')
    } else {
      // Regular navigation within the app
      router.push(`/${item.id}`)
    }
  }
}
</script>

<template>
  <div class="root-layout">
    <div class="content-wrapper">
      <BravoMainNavigation
        :items="filteredNavigationItems"
        :bottomItems="bottomItems"
        @update:items="(val: typeof filteredNavigationItems.value) => filteredNavigationItems = val"
        @update:bottomItems="(val: typeof bottomItems.value) => bottomItems = val" 
        @nav-click="handleNavClick"
      >
        <!-- Use the imported SVG -->
        <template #customIcon-ovation>
          <img :src="LogoSvg" alt="Ovation Logo" class="ovation-logo" />
        </template>
      </BravoMainNavigation>
      
      <main class="main-content">
        <RouterView />
      </main>

      <!-- Use the new ProfileMenu component -->
      <ProfileMenu 
        ref="profileMenu"
        :userStore="userStore"
        @logout="handleLogout" 
      />
      
      <!-- Add AnnouncekitWidget component -->
      <AnnouncekitWidget />
    </div>
  </div>
</template>

<style scoped>
.root-layout {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent scrolling on the root container */
}

.content-wrapper {
  flex: 1;
  display: flex;
  height: 100vh; /* Use full viewport height now that header is gone */
  min-height: 0; /* Important for nested flex containers */
}

.main-content {
  flex: 1;
  padding: 0;
  background: var(--surface-0);
  overflow-y: auto;
}

:deep(.bravo-main-navigation) {
  height: 100%;
  flex-shrink: 0; /* Prevent navigation from shrinking */
}

.ovation-logo {
  width: 24px;
  height: 24px;
  /* Center it within the navigation icon space */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animation classes that will be applied to icons */
:deep(.custom-ovation-logo.spin-animation),
.ovation-logo.spin-animation {
  animation: bounce-scale 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes bounce-scale {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.2);
  }
  60% {
    transform: scale(0.8);
  }
  80% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

:deep(.custom-ovation-logo) {
  background-image: url('../assets/logo.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 24px;
  height: 24px;
  /* Hide any text inside */
  font-size: 0;
  color: transparent;
}

/* Hide the AnnouncekitWidget div */
:deep(#announcekit-widget) {
  display: none !important;
}
</style> 