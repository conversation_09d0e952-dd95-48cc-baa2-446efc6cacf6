<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { mfeLoader } from '../services/MFELoaderService'
import { mfeAdapter } from '../services/MFEAdapterService'
import { getHttpClient } from '../services/httpClientProvider'
import { useAuthStore } from '../stores/auth'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'

interface EnvConfig {
  journeysUrl: string;
  journeysUrlNLO: string;
  tasksUrl: string;
  notificationsUrl: string;
}

const envConfigs: Record<string, EnvConfig> = {
  local: {
    journeysUrl: 'https://s-journeys-bf16bf5a-5xeedb3yvq-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-5xeedb3yvq-uc.a.run.app',
    tasksUrl: 'https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage',
    notificationsUrl: 'https://notify-svc-5xeedb3yvq-uc.a.run.app',
  },
  stage: {
    journeysUrl: 'https://s-journeys-bf16bf5a-5xeedb3yvq-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-5xeedb3yvq-uc.a.run.app',
    tasksUrl: 'https://us-central1-stage-microservices-7845.cloudfunctions.net/service-tasks-stage',
    notificationsUrl: 'https://notify-svc-5xeedb3yvq-uc.a.run.app',
  },
  preprod: {
    journeysUrl: 'https://s-journeys-627397ad-nfznudrdwa-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-nfznudrdwa-uc.a.run.app',
    tasksUrl: 'https://us-central1-preprod-microservices-9a06.cloudfunctions.net/service-tasks-preprod',
    notificationsUrl: 'https://notify-svc-nfznudrdwa-uc.a.run.app',
  },
  prod: {
    journeysUrl: 'https://s-journeys-8f5419eb-7hyimkhqqq-uc.a.run.app',
    journeysUrlNLO: 'https://journeys-7hyimkhqqq-uc.a.run.app',
    tasksUrl: 'https://us-central1-prod-microservices-d798.cloudfunctions.net/service-tasks-prod',
    notificationsUrl: 'https://notify-svc-7hyimkhqqq-uc.a.run.app',
  },
};

function getEnvConfig(): EnvConfig {
  const host = window.location.host;
  
  if (host.includes('local-env') || host.includes('localhost')) {
    return envConfigs.local;
  } else if (host.includes('stage')) {
    return envConfigs.stage;
  } else if (host.includes('preprod')) {
    return envConfigs.preprod;
  }
  return envConfigs.prod;
}

const isLoading = ref(true);
const error = ref<string | null>(null);

onMounted(async () => {
  try {
    // Initialize auth store
    const authStore = useAuthStore();
    // Get HTTP client
    const httpClient = getHttpClient(authStore);

    // Load the journey-builder MFE
    await mfeLoader.loadMFE('journey-builder');
    
    // Get the configuration for the MFE
    const config = mfeLoader.getMFEConfig('journey-builder');
    
    // Create the context for the journey-builder
    const context = {
      orgId: 'H3F', // TODO: Get from config/store
      userId: null,
      userPermissions: 'journey_manage',
      useNLO: true,
      usePathBuilder: true,
      vueflow: true,
    };

    // Get environment config
    const envConfig = getEnvConfig();

    // Mount the widget
    await mfeAdapter.mountWidget(
      config.key,
      '#journeys-list',
      {
        context,
        config: envConfig,
        adminHttpClient: httpClient
      }
    );
  } catch (e) {
    console.error('Failed to load journey-builder:', e);
    error.value = 'Failed to load Journey Builder. Please try refreshing the page.';
  } finally {
    isLoading.value = false;
  }
});
</script>

<template>
  <div class="journeys-view relative">
      <BravoProgressSpinner  v-show="isLoading"/>
      <div v-show="!isLoading" id="journeys-list"></div>
  </div>
</template>

<style scoped>
.journeys-view {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  height: 100%;
  width: 100%;
}

.content {
  margin-top: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
}

.error-container {
  color: var(--red-500);
  text-align: center;
  padding: 2rem;
}

#journeys-list {
  flex: 1;
  min-height: 0;
  width: 100%;

  &:deep(.p-datatable) {
    width: 100% !important;
  }

  &:deep(.journey-builder-list-view) {
    padding: 20px;
  }
}
</style> 