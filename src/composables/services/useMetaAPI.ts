import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface MetaResponse {
    success: boolean;
    message?: string;
    [key: string]: any;
}

/**
 * Composable that provides access to Meta API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useMetaAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchMetaData(): Promise<any> {
            console.log('🔍 MetaAPI: Fetching general metadata');
            try {            
                const data = await httpClient.get<MetaResponse>('admin/v4/core/', {
                    sAction: 'meta'
                });
                
                console.log('✅ MetaAPI: Metadata response received');

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch metadata', data);
                throw new Error(data.message || 'Failed to fetch metadata');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching metadata:', error);
                throw error;
            }
        },

        async fetchPartnerMetaData(): Promise<any> {
            console.log('🔍 MetaAPI: Fetching partner metadata');
            try {            
                const data = await httpClient.get<MetaResponse>('admin/v4/core/', {
                    sAction: 'metaPartners'
                });
                
                console.log('✅ MetaAPI: Partner metadata response received');

                if (data.success) {
                    return data;
                }
                
                console.error('❌ MetaAPI: Failed to fetch partner metadata', data);
                throw new Error(data.message || 'Failed to fetch partner metadata');
            } catch (error) {
                console.error('❌ MetaAPI: Error fetching partner metadata:', error);
                throw error;
            }
        }
    };
} 