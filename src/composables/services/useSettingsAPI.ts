import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface View {
    id: string;
    label: string;
}

export interface SettingsResponse {
    success: boolean;
    message?: string;
    relay_views?: {
        results: View[];
    };
}

/**
 * Composable that provides access to Settings API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useSettingsAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async fetchViews(): Promise<View[]> {
            console.log('🔍 SettingsAPI: Fetching views from settings API');
            try {            
                const data = await httpClient.get<SettingsResponse>('admin/v4/settings/', {
                    sAction: 'viewsListing',
                    page: '1',
                    start: '0',
                    limit: '50',
                });
                
                console.log('✅ SettingsAPI: Views response received:', data);

                if (data.success && data.relay_views?.results) {
                    console.log(`📊 SettingsAPI: Found ${data.relay_views.results.length} views`);
                    return data.relay_views.results;
                }
                
                console.error('❌ SettingsAPI: No views data in response', data);
                throw new Error(data.message || 'Failed to fetch views');
            } catch (error) {
                console.error('❌ SettingsAPI: Error fetching views:', error);
                throw error;
            }
        }
    };
} 