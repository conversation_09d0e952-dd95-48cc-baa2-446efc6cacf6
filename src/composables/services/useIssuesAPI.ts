import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export type CaseStatus = 'new' | 'ready' | 'active' | 'closed';

export interface Issue {
  id: string;
  display_name: string;
  availableComm: any[]; // We'll type this more specifically once we see the data
  source_data?: {
    source: string;
  };
}

export interface FetchCasesParams {
  page?: number;
  limit?: number;
  sort?: Array<{
    property: string;
    direction: 'ASC' | 'DESC';
  }>;
  filter?: Array<{
    property: string;
    value: any;
    operator?: string
  }>;
  start?: number;
  status?: CaseStatus;
}

export interface IssueResponse {
  success: boolean;
  message?: string;
  issues?: {
    results: Issue[];
  };
}

/**
 * Composable that provides access to Issues API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useIssuesAPI() {
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);

  return {
    async fetchIssue(issueId: string): Promise<Issue> {
      console.log('IssuesAPI: Fetching specific issue:', issueId);
      
      const data = await httpClient.get<IssueResponse>('admin/v4/issues/', {
        'sAction': 'listingCases',
        'id': issueId
      });

      console.log('IssuesAPI: Issue response:', data);
      
      if (data.success && data.issues?.results[0]) {
        return data.issues.results[0];
      }
      
      throw new Error(data.message || 'Failed to fetch issue');
    },

    async fetchViews(): Promise<any> {
      console.log('IssuesAPI: Fetching views');
      
      const data = await httpClient.get<IssueResponse>('admin/v4/settings/', {
        'sAction': 'viewsListing',
        page: '1',
        start: '0',
        limit: '50',
      });
      console.log('IssuesAPI: Views response:', data);
    
      if (data.success && data.issues?.results) {
        return data.issues.results;
      }
      
      throw new Error(data.message || 'Failed to fetch issue');
    },

    async fetchCases(
      params: FetchCasesParams,
    ): Promise<Issue[]> {
      console.log('IssuesAPI: Fetching issues');
      
      const data = await httpClient.get<IssueResponse>('admin/v4/issues/', {
        'sAction': 'listingCases',
        page: params.page?.toString() || '1',
        start: params.start?.toString() || '0',
        limit: params.limit?.toString() || '25',
        sort: params.sort
          ? JSON.stringify(
            params.sort.map(s => ({
              property: s.property,
              direction: s.direction,
            }))
          )
          : '[{"property":"updated","direction":"DESC"}]',
        filter: params.filter
          ? JSON.stringify(
            params.filter.map(f => ({
              property: f.property,
              value: f.value,
              operator: f.operator, 
            }))
          )
          : '[]',
      });
      console.log('IssuesAPI: Issue response:', data);
    
      if (data.success && data.issues?.results) {
        return data.issues.results;
      }
      
      throw new Error(data.message || 'Failed to fetch issue');
    }
  };
} 