import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';

export interface SSOCheckResponse {
    success: boolean;
    canAuthenticateInternal: boolean;
    canAuthenticateExternal: boolean;
    externalIdPs: Array<{
        logo: string | null;
        name: string;
        description: string;
        url: string;
    }>;
    current_server_time: string;
}

export interface UserProfileData {
    id: string;
    first_name: string;
    last_name?: string;
    email?: string;
    image?: string;
    nickname?: string;
    phone_number?: string;
    phone_ext?: string;
    time_zone?: string;
    country_id?: string;
    [key: string]: any;
}

export interface UserResponse {
    success: boolean;
    message?: string;
    data?: any;
    users?: any;
}

/**
 * Composable that provides access to User API functions.
 * This safely initializes the httpClient with authStore only when called 
 * inside components or other composables after Pinia is ready.
 */
export function useUserAPI() {
    const authStore = useAuthStore();
    const httpClient = getHttpClient(authStore);

    return {
        async checkSSO(email: string): Promise<SSOCheckResponse> {
            const response = await fetch(`${import.meta.env.VITE_BOOMTOWN_API_HOST}/admin/v4/core/?sAction=getUserAuth`, {
                method: 'POST',
                headers: {
                    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'x-boomtown-client-instance-id': authStore.clientInstanceId,
                    'x-boomtown-csrf-token': authStore.csrfToken,
                    'x-requested-with': 'XMLHttpRequest'
                },
                body: `email=${encodeURIComponent(email)}`,
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error('Failed to check SSO status');
            }

            return response.json();
        },

        async updateUserProfile(profileData: UserProfileData): Promise<UserResponse> {
            try {
                // Create URLSearchParams object for form-encoded data
                const payload = new URLSearchParams();
                payload.append('users', JSON.stringify([profileData]));
                payload.append('stringify', 'false');

                const data = await httpClient.post<UserResponse>(
                    'admin/v4/users/',
                    payload,
                    {
                        sAction: 'put',
                    },
                    {
                        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    }
                );

                if (data.success) {
                    return data;
                }

                throw new Error(data.message || 'Failed to update user profile');
            } catch (error) {
                console.error('Error updating user profile:', error);
                throw error;
            }
        },
        
        async getPartnerMetadata(): Promise<any> {
            return httpClient.get<any>('admin/v4/core/?sAction=metaPartners');
        }
    };
} 