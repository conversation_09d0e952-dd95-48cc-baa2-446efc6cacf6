/**
 * Froala Editor Configuration
 * 
 * A reusable configuration based on the settings found in the OvationCXM application.
 * This TypeScript version provides type safety for your Froala editor implementation.
 */

import { getHttpClient } from '@/services/httpClientProvider';
import { useAuthStore } from '@/stores/auth';
import type { AuthStoreInterface } from '@/stores/auth';

// Type definitions for Froala configuration
interface FroalaButtonGroup {
  buttons: string[];
  buttonsVisible?: number;
  align?: string;
}

interface FroalaToolbarButtons {
  [group: string]: FroalaButtonGroup;
}

interface FroalaEvent {
  (this: any, ...args: any[]): void;
}

interface FroalaEvents {
  [event: string]: FroalaEvent;
}

interface FroalaUploadParams {
  object?: string;
  object_id?: string;
  file_tag: string;
}

interface FroalaLinkItem {
  text: string;
  href: string;
  target?: string;
  displayText?: string;
}

interface FroalaClasses {
  [className: string]: string;
}

interface FroalaHelpCommand {
  val: string;
  desc: string;
}

interface FroalaHelpSet {
  title: string;
  commands: FroalaHelpCommand[];
}

interface FroalaCodeMirrorOptions {
  lineNumbers: boolean;
  tabMode: string;
  indentWithTabs: boolean;
  lineWrapping: boolean;
  mode: string;
  tabSize: number;
}

// Main configuration interface
interface FroalaConfig {
  // Basic Configuration
  placeholderText: string;
  charCounterMax: number;
  tooltipStrict: boolean;
  iframe: boolean;
  
  // Toolbar Configuration
  toolbarSticky: boolean;
  toolbarStickyOffset: number;
  toolbarButtons: FroalaToolbarButtons;
  
  // Content Styling
  paragraphStyles: { [style: string]: string };
  
  // Enter Key Behavior
  enter: 'P' | 'BR' | 'DIV';
  
  // File Upload Configuration
  imageUpload: boolean;
  imageUploadURL: string;
  imageUploadParams: FroalaUploadParams;
  imageUploadMethod: string;
  imageUploadWithCredentials: boolean;
  imageManagerXhrFunction: (() => XMLHttpRequest) | null;
  
  videoUpload: boolean;
  videoUploadURL: string;
  videoUploadParams: FroalaUploadParams;
  videoUploadMethod: string;
  videoUploadWithCredentials: boolean;
  videoManagerXhrFunction: (() => XMLHttpRequest) | null;
  
  fileUpload: boolean;
  fileUploadURL: string;
  fileUploadParams: FroalaUploadParams;
  fileUploadMethod: string;
  fileUploadWithCredentials: boolean;
  fileManagerXhrFunction: (() => XMLHttpRequest) | null;
  
  // Request Headers
  requestHeaders: { [header: string]: string };
  
  // Global request configuration
  requestWithCORS: boolean;
  requestWithCredentials: boolean;
  
  // Plugin Options
  saveInterval: number;
  saveURL: string | null;
  saveParams: object;
  saveParam: string;
  saveMethod: string;
  
  // Code View Configuration
  codeMirror: any;
  codeMirrorOptions: FroalaCodeMirrorOptions;
  
  // HTML Configuration
  htmlAllowedTags: string[];
  htmlAllowedAttrs: string[];
  
  // Word Paste Handling
  wordDeniedTags: string[];
  wordDeniedAttrs: string[];
  wordAllowedStyleProps: string[];
  
  // Font Awesome Support
  fontAwesomeTemplate: string;
  
  // Entities Configuration
  entities: string;
  
  // Link Configuration
  linkAlwaysBlank: boolean;
  linkAlwaysNoFollow: boolean;
  linkNoOpener: boolean;
  linkNoReferrer: boolean;
  linkList: FroalaLinkItem[];
  linkText: boolean;
  
  // Inline Classes
  inlineClasses: FroalaClasses;
  
  // Emoticons Configuration
  emoticonsUseImage: boolean;
  
  // Table Configuration
  tableStyles: { [style: string]: string };
  
  // Full Page Editing
  fullPage: boolean;
  
  // Help Feature Configuration
  helpSets: FroalaHelpSet[];
  
  // Events
  events: FroalaEvents;

  attribution: boolean;
  wordCounterCount: boolean;
  charCounterCount: boolean;
  key: string;
}

// Define a custom icon template for using custom icon sets
interface FroalaEditorStatic {
  DefineIconTemplate(name: string, template: string): void;
}

// Declare FroalaEditor as a global variable
declare global {
  interface Window {
    FroalaEditor: FroalaEditorStatic;
    CodeMirror: any;
  }
}

// If in a browser environment
if (typeof window !== 'undefined' && window.FroalaEditor) {
  window.FroalaEditor.DefineIconTemplate('custom_icons', '<i style="color: #626363;" class="icon icon-16 [NAME]"></i>');
}

// Export a function that returns the configuration with dynamic values
export default function buildFroalaConfig(options: { 
  object?: string; 
  object_id?: string;
  authStore?: AuthStoreInterface;
} = {}): FroalaConfig {
  // Use provided auth store or initialize it if not provided
  const authStore = options.authStore || useAuthStore();
  // Get HTTP client instance
  const httpClient = getHttpClient(authStore);
  
  const dsToken = httpClient.getCookieDSToken();
  
  // Create a custom XHR function that includes credentials and cookies
  const customXHR = function() {
    const xhr = new XMLHttpRequest();
    xhr.withCredentials = true;
    return xhr;
  };
  
  return {
    // Basic Configuration
    placeholderText: 'Enter content here...',
    charCounterMax: -1,
    tooltipStrict: true,
    iframe: false, // Enable/disable iframe mode
    key: '',
    // Toolbar Configuration
    toolbarSticky: true,
    toolbarStickyOffset: 0,
    attribution: false,
    wordCounterCount: false,
    charCounterCount: false,
    
    // Content Styling
    paragraphStyles: {
      'alert-info': 'Info/Notice',
      'alert-warning': 'Warning',
      'alert-danger': 'Danger',
      'alert-success': 'Success',
      'borderemphasis': 'Emphasized border',
    },
    
    // Enter Key Behavior
    enter: 'P', // Use <p> when hitting enter (set to 'BR' to use <br/>)
    
    // File Upload Configuration
    imageUpload: true,
    imageUploadURL: `${httpClient.url}/admin/v4/files/?sAction=putFile`,
    imageUploadParams: {
      object: options.object || '', // Use provided object or empty string
      object_id: options.object_id || '', // Use provided object_id or empty string
      file_tag: 'image',
    },
    imageUploadMethod: 'POST',
    imageUploadWithCredentials: true,
    imageManagerXhrFunction: customXHR,
    
    videoUpload: true,
    videoUploadURL: `${httpClient.url}/admin/v4/files/?sAction=putFile`,
    videoUploadParams: {
      object: options.object || '', // Use provided object or empty string
      object_id: options.object_id || '', // Use provided object_id or empty string
      file_tag: 'video',
    },
    videoUploadMethod: 'POST',
    videoUploadWithCredentials: true,
    videoManagerXhrFunction: customXHR,
    
    fileUpload: true,
    fileUploadURL: `${httpClient.url}/admin/v4/files/?sAction=putFile`,
    fileUploadParams: {
      object: options.object || '', // Use provided object or empty string
      object_id: options.object_id || '', // Use provided object_id or empty string
      file_tag: 'document',
    },
    fileUploadMethod: 'POST',
    fileUploadWithCredentials: true,
    fileManagerXhrFunction: customXHR,
    
    // Request Headers
    requestHeaders: {
      'x-boomtown-csrf-token': httpClient.csrf,
      'x-boomtown-client-instance-id': httpClient.instanceId,
    },
    
    // Global request configuration
    requestWithCORS: true,
    requestWithCredentials: true,
    
    // Plugin Options
    // Save Plugin Configuration
    saveInterval: 10000,
    saveURL: null,
    saveParams: {},
    saveParam: 'body',
    saveMethod: 'POST',
    
    // Code View Configuration
    codeMirror: typeof window !== 'undefined' ? window.CodeMirror : null,
    codeMirrorOptions: {
      lineNumbers: true,
      tabMode: 'indent',
      indentWithTabs: true,
      lineWrapping: true,
      mode: 'text/html',
      tabSize: 2
    },
    
    // HTML Configuration
    htmlAllowedTags: [
      'a', 'abbr', 'address', 'area', 'article', 'aside', 'audio', 'b', 'base', 'bdi', 'bdo', 'blockquote', 'br', 'button',
      'canvas', 'caption', 'cite', 'code', 'col', 'colgroup', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div',
      'dl', 'dt', 'em', 'embed', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'header', 'hgroup', 'hr', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'keygen', 'label', 'legend', 'li', 'link',
      'main', 'map', 'mark', 'menu', 'menuitem', 'meter', 'nav', 'noscript', 'object', 'ol', 'optgroup', 'option', 'output',
      'p', 'param', 'pre', 'progress', 'queue', 'rp', 'rt', 'ruby', 's', 'samp', 'script', 'section', 'select', 'small',
      'source', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot',
      'th', 'thead', 'time', 'title', 'tr', 'track', 'u', 'ul', 'var', 'video', 'wbr'
    ],
    htmlAllowedAttrs: [
      'accept', 'accept-charset', 'accesskey', 'action', 'align', 'allowfullscreen', 'allowtransparency', 'alt', 'async',
      'autocomplete', 'autofocus', 'autoplay', 'autosave', 'background', 'bgcolor', 'border', 'charset', 'cellpadding',
      'cellspacing', 'checked', 'cite', 'class', 'color', 'cols', 'colspan', 'content', 'contenteditable', 'contextmenu',
      'controls', 'coords', 'data', 'data-.*', 'datetime', 'default', 'defer', 'dir', 'dirname', 'disabled', 'download',
      'draggable', 'dropzone', 'enctype', 'for', 'form', 'formaction', 'frameborder', 'headers', 'height', 'hidden', 'high',
      'href', 'hreflang', 'http-equiv', 'icon', 'id', 'ismap', 'itemprop', 'keytype', 'kind', 'label', 'lang', 'language',
      'list', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'mozallowfullscreen', 'multiple', 'muted', 'name',
      'novalidate', 'open', 'optimum', 'pattern', 'ping', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate',
      'radiogroup', 'readonly', 'rel', 'required', 'reversed', 'rows', 'rowspan', 'sandbox', 'scope', 'scoped', 'scrolling',
      'seamless', 'selected', 'shape', 'size', 'sizes', 'span', 'src', 'srcdoc', 'srclang', 'srcset', 'start', 'step', 'summary',
      'spellcheck', 'style', 'tabindex', 'target', 'title', 'type', 'translate', 'usemap', 'value', 'valign', 'webkitallowfullscreen',
      'width', 'wrap', 'aria-.*'
    ],
    
    // Word Paste Handling
    wordDeniedTags: [],
    wordDeniedAttrs: [],
    wordAllowedStyleProps: [
      'font-family', 'font-size', 'background', 'color', 'width', 'text-align', 
      'vertical-align', 'background-color', 'padding', 'margin', 'height', 
      'margin-top', 'margin-left', 'margin-right', 'margin-bottom',
      'text-decoration', 'font-weight', 'font-style', 'border'
    ],
    
    // Font Awesome Support
    fontAwesomeTemplate: '<i class="fa fa-[NAME] fr-deletable" aria-hidden="true"></i>',
    
    // Entities Configuration
    entities: '&quot;&#39;&iexcl;&cent;&pound;&curren;&yen;&brvbar;&sect;&uml;&copy;&ordf;&laquo;&not;&shy;&reg;&macr;&deg;&plusmn;&sup2;&sup3;&acute;&micro;&para;&middot;&cedil;&sup1;&ordm;&raquo;&frac14;&frac12;&frac34;&iquest;&Agrave;&Aacute;&Acirc;&Atilde;&Auml;&Aring;&AElig;&Ccedil;&Egrave;&Eacute;&Ecirc;&Euml;&Igrave;&Iacute;&Icirc;&Iuml;&ETH;&Ntilde;&Ograve;&Oacute;&Ocirc;&Otilde;&Ouml;&times;&Oslash;&Ugrave;&Uacute;&Ucirc;&Uuml;&Yacute;&THORN;&szlig;&agrave;&aacute;&acirc;&atilde;&auml;&aring;&aelig;&ccedil;&egrave;&eacute;&ecirc;&euml;&igrave;&iacute;&icirc;&iuml;&eth;&ntilde;&ograve;&oacute;&ocirc;&otilde;&ouml;&divide;&oslash;&ugrave;&uacute;&ucirc;&uuml;&yacute;&thorn;&yuml;&OElig;&oelig;&Scaron;&scaron;&Yuml;&fnof;&circ;&tilde;&Alpha;&Beta;&Gamma;&Delta;&Epsilon;&Zeta;&Eta;&Theta;&Iota;&Kappa;&Lambda;&Mu;&Nu;&Xi;&Omicron;&Pi;&Rho;&Sigma;&Tau;&Upsilon;&Phi;&Chi;&Psi;&Omega;&alpha;&beta;&gamma;&delta;&epsilon;&zeta;&eta;&theta;&iota;&kappa;&lambda;&mu;&nu;&xi;&omicron;&pi;&rho;&sigmaf;&sigma;&tau;&upsilon;&phi;&chi;&psi;&omega;&thetasym;&upsih;&piv;&ensp;&emsp;&thinsp;&zwnj;&zwj;&lrm;&rlm;&ndash;&mdash;&lsquo;&rsquo;&sbquo;&ldquo;&rdquo;&bdquo;&dagger;&Dagger;&bull;&hellip;&permil;&prime;&Prime;&lsaquo;&rsaquo;&oline;&frasl;&euro;&image;&weierp;&real;&trade;&alefsym;&larr;&uarr;&rarr;&darr;&harr;&crarr;&lArr;&uArr;&rArr;&dArr;&hArr;&forall;&part;&exist;&empty;&nabla;&isin;&notin;&ni;&prod;&sum;&minus;&lowast;&radic;&prop;&infin;&ang;&and;&or;&cap;&cup;&int;&there4;&sim;&cong;&asymp;&ne;&equiv;&le;&ge;&sub;&sup;&nsub;&sube;&supe;&oplus;&otimes;&perp;&sdot;&lceil;&rceil;&lfloor;&rfloor;&lang;&rang;&loz;&spades;&clubs;&hearts;&diams;',
    
    // Link Configuration
    linkAlwaysBlank: false,
    linkAlwaysNoFollow: false,
    linkNoOpener: true,
    linkNoReferrer: true,
    linkList: [
      {
        text: 'Froala',
        href: 'https://froala.com',
        target: '_blank'
      },
      {
        text: 'Google',
        href: 'https://google.com',
        target: '_blank'
      }
    ],
    linkText: true,
    
    // Inline Classes
    inlineClasses: {
      'fr-class-code': 'Code',
      'fr-class-highlighted': 'Highlighted',
      'fr-class-transparent': 'Transparent'
    },
    
    // Emoticons Configuration
    emoticonsUseImage: true,
    
    // Table Configuration
    tableStyles: {
      'fr-solid-borders': 'Solid Borders',
      'fr-dashed-borders': 'Dashed Borders',
      'fr-alternate-rows': 'Alternate Rows'
    },
    
    // Full Page Editing
    fullPage: false,
    
    // Help Feature Configuration
    helpSets: [
      {
        title: 'Inline Editor',
        commands: [
          { val: 'OSkeyE', desc: 'Show the editor' }
        ]
      }
    ],
    
    // Toolbar Buttons Configuration (common configuration)
    toolbarButtons: {
      'moreText': {
        'buttons': ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontSize', 'textColor', 'backgroundColor', 'clearFormatting'],
        'buttonsVisible': 4
      },
      'moreParagraph': {
        'buttons': ['alignLeft', 'alignCenter', 'alignRight', 'alignJustify', 'formatOL', 'formatUL', 'paragraphFormat', 'paragraphStyle', 'lineHeight', 'outdent', 'indent', 'quote'],
        'buttonsVisible': 3
      },
      'moreRich': {
        'buttons': ['insertLink', 'insertImage', 'insertVideo', 'insertTable', 'emoticons', 'fontAwesome', 'specialCharacters', 'embedly', 'insertFile', 'insertHR'],
        'buttonsVisible': 3
      },
      'moreMisc': {
        'buttons': ['undo', 'redo', 'fullscreen', 'html', 'print', 'spellChecker', 'help'],
        'buttonsVisible': 2,
        'align': 'right'
      }
    },
    
    // Events
    events: {
      // Example events - should be customized as needed
      'initialized': function() {
        // Add initialization logic here
      },
      'contentChanged': function() {
        // Handle content changes here
      },
      'focus': function() {
        // Handle focus events
      },
      'blur': function() {
        // Handle blur events
      }
    }
  };
} 
