import type { 
  InteractionEvent, 
  FetchInteractionEventsParams, 
  InteractionEventsResponse 
} from '@/composables/services/useInteractionEventsAPI';

/**
 * @deprecated Use the useInteractionEventsAPI composable from '@/composables/services' instead
 * This class is kept for backward compatibility but should not be used in new code
 */
class InteractionEventsAPI {
  constructor() {
    console.warn('InteractionEventsAPI is deprecated. Use useInteractionEventsAPI composable from @/composables/services instead.');
  }

  async fetchInteractionEvents(
    params: FetchInteractionEventsParams
  ): Promise<InteractionEvent[]> {
    console.warn('InteractionEventsAPI.fetchInteractionEvents is deprecated. Use useInteractionEventsAPI composable from @/composables/services instead.');
    return [];
  }
}

export type { 
  InteractionEvent, 
  FetchInteractionEventsParams, 
  InteractionEventsResponse 
};
export const interactionEventsAPI = new InteractionEventsAPI(); 