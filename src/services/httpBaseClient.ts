import { v4 as uuidv4 } from 'uuid';
import { recordApiResponse } from '@/utils/apiRecorder';

interface RequestOptions {
    params?: Record<string, string>;
    body?: any;
    headers?: Record<string, string>;
}

export class HttpBaseClient {
    protected baseUrl: string;

    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
    }

    protected async request<T>(
        method: string, 
        path: string, 
        options: RequestOptions = {},
        additionalHeaders: Record<string, string> = {}
    ): Promise<T> {
        // Build the query string if we have params
        let queryString = '';
        if (options.params) {
            const searchParams = new URLSearchParams();
            Object.entries(options.params).forEach(([key, value]) => {
                searchParams.append(key, value);
            });
            queryString = `?${searchParams.toString()}`;
        }

        let reqBody = options.body || undefined;
        
        // Special handling for URLSearchParams - don't stringify these
        const isFormData = reqBody instanceof URLSearchParams;
        
        if (reqBody && !isFormData) {
            // Only stringify if not URLSearchParams
            reqBody = JSON.stringify(reqBody);
        }

        // Combine base URL (if relative path) or use absolute path
        const fullUrl = `${this.baseUrl}/${path.replace(/^\//, '')}${queryString}`;

        // Set the correct content type if it's form data
        const contentType = isFormData 
            ? 'application/x-www-form-urlencoded; charset=UTF-8'
            : 'application/json';

        const headers = {
            'content-type': contentType,
            'x-request-id': uuidv4(),
            ...options.headers,
            ...additionalHeaders
        };

        const response = await fetch(fullUrl, {
            method,
            headers,
            body: reqBody,
            credentials: 'include',
            mode: 'cors',
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();

        // Record the API response
        const functionName = new Error().stack?.split('\n')[2]?.match(/at\s+(\w+)/)?.[1] || 'unknown';
        recordApiResponse(functionName, fullUrl, method, responseData);

        return responseData;
    }

    async get<T>(path: string, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
        return this.request<T>('GET', path, { params, headers });
    }

    async post<T>(path: string, body: any, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
        return this.request<T>('POST', path, { body, params, headers });
    }

    async put<T>(path: string, body: any, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
        return this.request<T>('PUT', path, { body, params, headers });
    }

    async patch<T>(path: string, body: any, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
        return this.request<T>('PATCH', path, { body, params, headers });
    }

    async delete<T>(path: string, params?: Record<string, string>, headers?: Record<string, string>): Promise<T> {
        return this.request<T>('DELETE', path, { params, headers });
    }

    get url(): string {
        return this.baseUrl;
    }
} 