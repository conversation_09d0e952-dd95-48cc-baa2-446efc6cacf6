interface RequestInfo {
  headers?: Record<string, string>;
  params?: Record<string, string>;
}

class AdminHttpClient {
  #baseUrl: string;

  constructor(baseUrl: string) {
    this.#baseUrl = baseUrl;
    if (!window.fetch) {
      throw new Error("Unable to create AdminHttpClient: Fetch API not found.");
    }
  }

  async request(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    body: Record<string, any> | null = null,
    baseRequestInfo: RequestInfo = {}
  ): Promise<any> {
    const { params, ...requestInfo } = baseRequestInfo;
    const url = new URL(this.#baseUrl);

    // Ensure endpoint starts with a slash and baseURL doesn't end with one
    endpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
    url.pathname =
      (url.pathname.endsWith("/") ? url.pathname.slice(0, -1) : url.pathname) +
      endpoint;

    // Append params to url
    let searchParams = url.searchParams.toString();
    if (searchParams.length) {
      searchParams += "&" + new URLSearchParams(params).toString();
    } else {
      searchParams = new URLSearchParams(params).toString();
    }
    url.search = searchParams;

    // Most endpoints receive urlencoded form data
    const methodsWithBody = ["POST", "PUT", "PATCH"];
    if (methodsWithBody.includes(method) && body) {
      requestInfo.headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        ...requestInfo.headers,
      };
    }

    try {
      const response = await fetch(url.toString(), {
        method,
        headers: {
          ...requestInfo.headers,
          // Add any additional headers here
        },
        credentials: "include",
        body: this._serializeBody(body, requestInfo.headers?.["Content-Type"]),
      });

      if (!response.ok) {
        throw new Error(response.statusText);
      }
      return await response.json();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  get(endpoint: string, requestInfo: RequestInfo = {}): Promise<any> {
    return this.request(endpoint, "GET", null, requestInfo);
  }

  post(endpoint: string, body: Record<string, any>, requestInfo: RequestInfo = {}): Promise<any> {
    return this.request(endpoint, "POST", body, requestInfo);
  }

  put(endpoint: string, body: Record<string, any>, requestInfo: RequestInfo = {}): Promise<any> {
    return this.request(endpoint, "PUT", body, requestInfo);
  }

  patch(endpoint: string, body: Record<string, any>, requestInfo: RequestInfo = {}): Promise<any> {
    return this.request(endpoint, "PATCH", body, requestInfo);
  }

  delete(endpoint: string, requestInfo: RequestInfo = {}): Promise<any> {
    return this.request(endpoint, "DELETE", null, requestInfo);
  }

  private _serializeBody(body: Record<string, any> | null, contentType?: string): string | null {
    if (!body) {
      return null;
    }
    if (contentType === "application/x-www-form-urlencoded") {
      return new URLSearchParams(body).toString();
    } else if (contentType === "application/json") {
      return JSON.stringify(body);
    }
    return JSON.stringify(body);
  }

  setBaseUrl(baseUrl: string): void {
    this.#baseUrl = baseUrl;
  }
}

interface WidgetContext {
  [key: string]: any;
}

interface WidgetInit {
  adminHttpClient: AdminHttpClient;
  context: WidgetContext;
  config: Record<string, any>;
}

interface WidgetInstance {
  updateContext: (context: WidgetContext) => void;
  mount: (element: string | Element, context: WidgetContext) => Promise<void>;
  unmount: () => void;
}

type WidgetLike = (init: WidgetInit) => WidgetInstance;

class DynamicWidgetAdapter {
  #widgets: Record<string, WidgetLike> = {};
  #httpClient: AdminHttpClient;
  #config: Record<string, any> = {};

  constructor(config: { baseUrl?: string } = {}) {
    this.#httpClient = new AdminHttpClient(
      config.baseUrl || "https://app.goboomtown.com/api"
    );
  }

  setBaseUrl(baseUrl: string): this {
    this.#httpClient.setBaseUrl(baseUrl);
    return this;
  }

  get httpClient(): AdminHttpClient {
    return this.#httpClient;
  }

  setConfig(config: Record<string, any>): this {
    this.#config = config;
    return this;
  }

  registerWidget(name: string, widget: WidgetLike): void {
    if (typeof name !== "string" || name.length === 0) {
      throw new Error("Component name must be a non-empty string");
    }
    if (typeof widget !== "function") {
      throw new Error("Widget must be a function");
    }
    if (this.#widgets[name]) {
      console.warn(`Widget '${name}' already registered, overwriting...`);
    }
    this.#widgets[name] = widget;
  }

  async mountWidget(
    name: string,
    element: string | Element,
    context: WidgetContext = {}
  ): Promise<WidgetInstance> {
    // debugger
    const widget = this.#widgets[name];
    if (!widget) {
      console.log(
        "mountWidget() no widget available yet, adding listener to create instance and mount",
        name
      );
      return new Promise((resolve) => {
        document.addEventListener("registerDynamicWidget", async (e: CustomEvent) => {
          if (e.detail.name === name) {
            const instance = await this.createInstanceAndMount(
              e.detail.widget,
              element,
              context
            );
            resolve(instance);
          }
        });
      });
    } else {
      console.log("mountWidget() widget is available, creating instance and mounting", name);
      return this.createInstanceAndMount(widget, element, context);
    }
  }

  private async createInstanceAndMount(
    widget: WidgetLike,
    element: string | Element,
    context: WidgetContext = {}
  ): Promise<WidgetInstance> {
    // debugger
    const instance = widget({
      context: context.context,
      config: context.config,
      adminHttpClient: context.adminHttpClient,
    });
    console.log("created instance", instance);
    await instance.mount(element, context.context);
    return instance;
  }
}

// Create and export a singleton instance
export const mfeAdapter = new DynamicWidgetAdapter({
  baseUrl: window.location.origin + '/api'
});

// Initialize global adapter if it doesn't exist
if (!window.DynamicWidgetAdapter) {
  window.DynamicWidgetAdapter = mfeAdapter;

  document.addEventListener("registerDynamicWidget", (e: CustomEvent) => {
    const { name, widget } = e.detail;
    console.log("registerDynamicWidget", name, widget);
    mfeAdapter.registerWidget(name, widget);
  });

  const dynamicWidgetAdapterLoadedEvent = new Event("DynamicWidgetAdapterLoaded");
  document.dispatchEvent(dynamicWidgetAdapterLoadedEvent);
}

// Add type declaration for the global DynamicWidgetAdapter
declare global {
  interface Window {
    DynamicWidgetAdapter?: DynamicWidgetAdapter;
  }
}

// Add proper type for custom events
declare global {
  interface DocumentEventMap {
    registerDynamicWidget: CustomEvent;
  }
}

export type { 
  RequestInfo,
  WidgetContext,
  WidgetInit,
  WidgetInstance,
  WidgetLike
};
export { AdminHttpClient, DynamicWidgetAdapter }; 