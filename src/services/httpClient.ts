import { v4 as uuidv4 } from 'uuid';
import { recordApiResponse } from '@/utils/apiRecorder';
import { HttpBaseClient } from '@/services/httpBaseClient';
import type { AuthStoreInterface } from '@/stores/auth';

interface RequestOptions {
    params?: Record<string, string>;
    body?: any;
    headers?: Record<string, string>;
}

class HttpClient extends HttpBaseClient {
    private clientInstanceId: string;
    private onAuthChange?: (isAuthenticated: boolean) => void;
    private initialized: boolean = false;
    private initializing: Promise<void> | null = null;
    private isPartnerDataInitialized: boolean = false;
    private authStore: AuthStoreInterface | null = null;
    
    constructor(baseUrl: string, clientInstanceId: string, onAuthChange?: (isAuthenticated: boolean) => void) {
        super(baseUrl);
        this.clientInstanceId = clientInstanceId;
        this.onAuthChange = onAuthChange;
    }

    // Method to set auth store reference
    public setAuthStore(authStore: AuthStoreInterface): void {
        this.authStore = authStore;
    }

    private getCommonHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'x-boomtown-client-instance-id': this.clientInstanceId,
            'x-request-id': uuidv4(),
            'content-type': 'application/json',
        };

        // Get CSRF token from auth store if available
        if (this.authStore && this.authStore.csrfToken) {
            headers['x-boomtown-csrf-token'] = this.authStore.csrfToken;
        }

        return headers;
    }

    private hasRelayCookie(): boolean {
        return document.cookie.split(';').some((cookie) => cookie.trim().startsWith('relay='));
    }

    async initialize(): Promise<void> {
        // If already initialized or initializing, return existing promise
        if (this.initialized) return Promise.resolve();
        if (this.initializing) return this.initializing;

        this.initializing = new Promise<void>(async (resolve) => {
            try {
                // Just mark as initialized, auth should be handled by auth store
                this.initialized = true;
                resolve();
            } catch (error) {
                console.debug('Error during initialization:', error);
                this.initialized = true;
                resolve(); // Resolve anyway to allow the app to continue
            } finally {
                this.initializing = null;
            }
        });

        return this.initializing;
    }

    async refreshPartnerData(): Promise<void> {
        if (this.isPartnerDataInitialized) return;
        this.isPartnerDataInitialized = true;
        await this.getPartnerMetadata();
    }

    protected async request<T>(method: string, path: string, options: RequestOptions = {}): Promise<T> {
        // Build the query string if we have params
        let queryString = '';
        if (options.params) {
            const searchParams = new URLSearchParams();
            Object.entries(options.params).forEach(([key, value]) => {
                searchParams.append(key, value);
            });
            queryString = `?${searchParams.toString()}`;
        }

        let reqBody = options.body || undefined;

        if (reqBody) {
            let ignoreStringify = reqBody.stringify === false;

            if (reqBody instanceof URLSearchParams) {
                ignoreStringify = reqBody.get('stringify') === 'false';
            }

            delete options.body.stringify;

            reqBody = ignoreStringify ? reqBody : JSON.stringify(reqBody);
        }

        // Combine base URL (if relative path) or use absolute path
        const fullUrl = `${this.baseUrl}/${path.replace(/^\//, '')}${queryString}`;

        const response = await fetch(fullUrl, {
            method,
            headers: {
                ...this.getCommonHeaders(),
                ...options.headers,
            },
            body: reqBody,
            credentials: 'include',
            mode: 'cors',
        });

        if (!response.ok) {
            if (response.status === 401) {
                // Clear auth state and trigger redirect only on actual 401s from API calls
                console.debug('Received 401 from API, triggering logout');
                this.onAuthChange?.(false);
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();

        // Record the API response
        const functionName = new Error().stack?.split('\n')[2]?.match(/at\s+(\w+)/)?.[1] || 'unknown';
        recordApiResponse(functionName, fullUrl, method, responseData);

        return responseData;
    }

    getCookieDSToken(): string {
        // Look for the dstoken cookie in the document cookies
        const cookies = document.cookie.split(';');
        for (const cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'dstoken') {
                return value;
            }
        }
        return '';
    }
  
    get url(): string {
        return this.baseUrl;
    }

    // Getter for client instance ID
    get instanceId(): string {
        return this.clientInstanceId;
    }

    // Getter for CSRF token from auth store
    get csrf(): string {
        return this.authStore?.csrfToken || '';
    }

    // Add isInitialized getter
    get isInitialized(): boolean {
        return this.initialized;
    }

    // Add getter for authentication status from auth store
    get isAuthenticated(): boolean {
        return Boolean(this.authStore?.isAuthenticated);
    }

    async getPartnerMetadata(): Promise<any> {
        await this.initialize();
        return this.get<any>('admin/v4/core/?sAction=metaPartners');
    }
}

// Export the class for creating instances
export { HttpClient };
