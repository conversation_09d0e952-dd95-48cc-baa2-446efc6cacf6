<template>
  <iframe ref="iframe" width="100%" height="600" frameborder="0"></iframe>
</template>

<script lang="ts">
export default {
  name: 'ArticlePreview',
  props: {
    content: {
      type: String,
      required: true,
    },
    screensUrl: {
      type: String,
      required: false,
      default: 'https://f.goboomtown.com/screens',
    },
    articleTheme: {
      type: String,
      required: false,
      default: 'modern',
    },
    libraryCss: {
      type: String,
      required: false,
      default: '',
    },
  },
  mounted() {
    this.renderContent();
  },
  methods: {
    renderContent() {
      const iframe = this.$refs.iframe as HTMLIFrameElement;
      const doc = iframe.contentDocument || (iframe.contentWindow?.document);
      const baseUrl = this.screensUrl;

      const styles = `
      <style>
        ${this.libraryCss}
      </style>
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/kb.css">
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/header.css">
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/accessibility.css">
      <link rel="stylesheet" type="text/css" href="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/css/foundation.min.css">
      <link rel="stylesheet" href="${baseUrl}/assets/stylesheets/all.min.css">
      <link rel="stylesheet" href="${baseUrl}/assets/stylesheets/codesnippets.css">
      <link rel="stylesheet" type="text/css" href="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/css/normalize.css">
      `;

      const scripts = `
      <script src="${baseUrl}/assets/javascripts/jquery/3.5.1/jquery-3.5.1.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/js/foundation.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/jquery/plugins/jquery.waitforimages.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/scrollnav-3.0.2/scrollnav.min.umd.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/js/vendor/modernizr.js"><\/script>
      <script src="${baseUrl}/article/${this.articleTheme}/javascripts/default.js"><\/script>
      <script src="${baseUrl}/article/shared/javascripts/kbSearch.js"><\/script>
      <script src="${baseUrl}/article/shared/javascripts/kbArticle.js"><\/script>
      `;

      const html = `
      <!DOCTYPE html>
      <html>
        <head>
          ${styles}
          ${scripts}
        </head>
        <body>
          ${this.content}
        </body>
      </html>
      `;

      if (doc) {
        doc.open();
        doc.writeln(html);
        doc.close();
      }
    }
  },
  watch: {
    // Re-render when content or CSS changes
    content() {
      this.renderContent();
    },
    libraryCss() {
      this.renderContent();
    }
  }
};
</script>
