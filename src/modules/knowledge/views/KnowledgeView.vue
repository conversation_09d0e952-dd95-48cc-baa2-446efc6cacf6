<script setup lang="ts">
import { useKnowledgeStore } from '../stores/knowledge';
import { useMetaStore } from '@/stores/meta';
import { ref, watch, computed, onMounted } from 'vue';
import SidePanel from '../components/KnowledgeSidePanel.vue';
import { useRoute } from 'vue-router';
import Skeleton from 'primevue/skeleton';

import type { KnowledgeItem } from '../../../services/KnowledgeAPI';

const store = useKnowledgeStore();
const metaStore = useMetaStore();
const route = useRoute();
const loading = ref(store.loading);

// Load metadata when component mounts
onMounted(async () => {
    try {
        await metaStore.loadMetaData();
        await metaStore.loadPartnerMetaData();
    } catch (err) {
        console.error('Failed to load metadata:', err);
    }
});

// Watch for changes in the store's loading state
watch(
    () => store.loading,
    (newValue) => {
        loading.value = newValue;
    }
);

// Get the current node from the store
const selectedItem = computed<KnowledgeItem | null>(() => store.currentNode);

// Determine whether to show the list or an article
const viewType = computed(() => {
    if (!selectedItem.value) {
        return 'placeholder';
    }
    return selectedItem.value.type === 'folder' ? 'list' : 'article';
});

// Check if we're on an article detail page
const isArticleDetail = computed(() => {
    return route.name === 'knowledge-article';
});
</script>

<template>
    <div class="knowledge-view" :class="{ 'no-sidebar': isArticleDetail }">
        <SidePanel v-if="!isArticleDetail" />
        <div class="content-area">
            <!-- Router view for nested routes -->
            <router-view />
        </div>
    </div>
</template>

<style scoped>
.knowledge-view {
    display: flex;
    height: 100%;
    width: 100%;
}

.knowledge-view.no-sidebar {
    display: block;
}

.content-area {
    flex: 1;
    padding: 0rem;
    overflow-y: auto;
    background-color: #fff;
}

.knowledge-view.no-sidebar .content-area {
    padding: 0;
}

.loading-state,
.error-state,
.placeholder-state {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #64748b;
    text-align: center;
}

.placeholder-container {
    max-width: 400px;

    h2 {
        margin-top: 1.5rem;
        color: #1e293b;
    }

    p {
        margin-top: 0.5rem;
        color: #64748b;
    }

    i {
        color: #94a3b8;
    }
}

.knowledge-content {
    max-width: 800px;
    margin: 0 auto;

    h1 {
        font-size: 2rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }

    .metadata {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        font-size: 0.875rem;
        color: #64748b;
        margin-bottom: 2rem;

        .created-date,
        .updated-date {
            display: inline-flex;
            align-items: center;

            &::before {
                font-family: 'PrimeIcons';
                margin-right: 0.5rem;
            }
        }

        .created-date::before {
            content: '\e934'; /* pi-calendar icon */
        }

        .updated-date::before {
            content: '\e924'; /* pi-clock icon */
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;

            .tag {
                background-color: #e2e8f0;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                font-size: 0.75rem;
            }
        }
    }

    .content {
        font-size: 1rem;
        line-height: 1.6;
        color: #334155;

        h2 {
            font-size: 1.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        h3 {
            font-size: 1.25rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        p {
            margin-bottom: 1rem;
        }

        ul,
        ol {
            margin: 1rem 0;
            padding-left: 1.5rem;

            li {
                margin-bottom: 0.5rem;
            }
        }

        code {
            background-color: #f1f5f9;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }

        pre {
            background-color: #f1f5f9;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;

            code {
                background-color: transparent;
                padding: 0;
            }
        }

        blockquote {
            border-left: 4px solid #e2e8f0;
            padding-left: 1rem;
            color: #64748b;
            margin: 1rem 0;
        }
    }
}

/* Skeleton loader styles */
.skeleton-table {
    width: 100%;
}

.skeleton-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    width: 100%;
}
</style>
../features/kb/stores/knowledge
