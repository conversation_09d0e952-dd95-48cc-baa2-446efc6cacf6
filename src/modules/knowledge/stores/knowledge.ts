import { defineStore } from 'pinia';
import { useKnowledgeAPI } from '@/composables/services';
import type {
    KnowledgeNode,
    KnowledgeItem,
    ListingParams,
    KnowledgeLibrary,
    TeamAccess,
    KnowledgeRevision,
    KnowledgeBaseArticle,
    KnowledgeEvent,
} from '../../../services/KnowledgeAPI';

// Define the interface outside of the store
interface KnowledgeListingParams {
    rootParentId: string | null;
    parentId: string | null;
}

export const useKnowledgeStore = defineStore('knowledge', {
    state: () => ({
        items: [] as KnowledgeItem[],
        treeNodes: [] as KnowledgeNode[],
        currentNode: null as KnowledgeItem | null,
        currentArticle: null as KnowledgeItem | null,
        currentRootId: null as string | null,
        loading: false,
        loadingContent: false,
        error: null as string | null,
        selectedCategory: '',
        showFavorites: false,
        libraries: [] as KnowledgeLibrary[],
        loadingLibraries: false,
        selectedLibrary: null as KnowledgeLibrary | null,
        searchQuery: '',
        statusFilter: 'all',
        currentList: [] as KnowledgeItem[],
        totalCount: 0,
        selectedNodeKey: {} as Record<string, boolean>,
        teams: [] as TeamAccess[],
        loadingTeams: false,
        availableKnowledgeBases: [] as KnowledgeBaseArticle[],
        loadingKnowledgeBases: false,
        labelTree: [] as KnowledgeNode[],
        labelMap: {} as Record<string, string>,
        comments: [] as KnowledgeEvent[],
        loadingComments: false,
        commentsError: null as string | null,
        totalCommentsCount: 0,
    }),

    getters: {
        getItemById: (state) => {
            return (id: string) => state.items.find((item) => item.id === id);
        },
        getItemsByTag: (state) => {
            return (tag: string) =>
                state.items.filter((item) => item.metadata?.tags && item.metadata.tags.includes(tag));
        },
        filteredItems: (state) => {
            console.log('Computing filteredItems with', state.items.length, 'items');

            // If items array is empty, just return it
            if (!state.items || state.items.length === 0) {
                console.log('Items array is empty, returning empty array');
                return [];
            }

            // Check if there are any filters active
            const hasFilters =
                state.selectedCategory || state.searchQuery || state.statusFilter !== 'all' || state.showFavorites;

            // If no filters, return all items
            if (!hasFilters) {
                console.log('No filters active, returning all items:', state.items.length);
                return state.items;
            }

            // Otherwise apply filters
            const filtered = state.items.filter((item) => {
                // Skip items without required properties for filtering
                if (!item) return false;

                // Category filter
                const matchesCategory = !state.selectedCategory || item.metadata?.category === state.selectedCategory;

                // Search filter - make case insensitive
                const searchLower = state.searchQuery.toLowerCase();
                const matchesSearch = !searchLower || (item.title && item.title.toLowerCase().includes(searchLower));

                // Status filter
                const matchesStatus = state.statusFilter === 'all' || item.status === state.statusFilter;

                // Favorites filter (not implemented yet, just a placeholder)
                const matchesFavorites =
                    !state.showFavorites || (item.metadata?.tags && item.metadata.tags.includes('favorite'));

                return matchesCategory && matchesSearch && matchesStatus && matchesFavorites;
            });

            console.log('Filtered items from', state.items.length, 'to', filtered.length);
            return filtered;
        },
    },

    actions: {
        async fetchKnowledgeTree() {
            this.loading = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                this.treeNodes = await knowledgeAPI.fetchKnowledgeTree();
                console.log('Fetched knowledge tree:', this.treeNodes);
                return this.treeNodes;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch knowledge tree';
                console.error('Error fetching knowledge tree:', err);
                // Don't throw here, let the component handle the error display
                return [];
            } finally {
                this.loading = false;
            }
        },

        // Fetch tree nodes without affecting other state like current list or loading state
        async fetchKnowledgeTreeOnly() {
            try {
                const knowledgeAPI = useKnowledgeAPI();
                this.treeNodes = await knowledgeAPI.fetchKnowledgeTree();
                console.log('Fetched knowledge tree (tree-only update):', this.treeNodes);
                return this.treeNodes;
            } catch (err) {
                console.error('Error fetching knowledge tree:', err);
                // Don't throw here, just return empty array
                return [];
            }
        },

        async fetchChildNodes(nodeId: string) {
            this.loading = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const children = await knowledgeAPI.fetchKnowledgeTree({ node: nodeId });
                // Find the node with this ID and update its children
                this.updateNodeChildren(this.treeNodes, nodeId, children);
                console.log('Updated tree nodes with children for node:', nodeId);
                return children;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch child nodes';
                console.error('Error fetching child nodes:', err);
                // Still throw the error so component can handle it with toast
                throw err;
            } finally {
                this.loading = false;
            }
        },

        // Helper function to recursively update children of a node
        updateNodeChildren(nodes: KnowledgeNode[], nodeId: string, children: KnowledgeNode[]) {
            for (const node of nodes) {
                if (node.id === nodeId) {
                    node.children = children;
                    return true;
                }
                if (node.children && this.updateNodeChildren(node.children, nodeId, children)) {
                    return true;
                }
            }
            return false;
        },

        async fetchKnowledgeItem(id: string) {
            this.loading = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.fetchKnowledgeItem(id);
                this.currentNode = response.data || null;
                return response;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch knowledge item';
                console.error('Error fetching knowledge item:', err);
                throw err;
            } finally {
                this.loading = false;
            }
        },

        async fetchKnowledgeListing(params: KnowledgeListingParams, additionalParams?: ListingParams) {
            this.loadingContent = true;
            this.error = null;
            this.currentRootId = params.rootParentId;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const apiParams: ListingParams = additionalParams || {
                    page: 1,
                    start: 0,
                    limit: 50,
                    filter: [
                        { property: 'type', value: 0 },
                        { property: 'id', value: '_no_filter_' },
                        { property: 'parent_id', value: params.parentId },
                        { property: 'root_parent_id', value: params.rootParentId },
                        { property: 'partner_ids', operator: 'intersect_set', value: null },
                        { property: 'visibility', operator: '=', value: null },
                        { property: 'status', operator: '=', value: null },
                        { property: 'owner_partner_id', value: '_no_filter_' },
                        { property: 'dict_id', value: null },
                        { property: 'tag_ids', value: null },
                    ],
                };

                // If we're using default params, make sure parent_id and root_parent_id are set correctly
                if (!additionalParams && apiParams.filter) {
                    // Find and update the parent_id filter
                    const parentIdIndex = apiParams.filter.findIndex((f) => f.property === 'parent_id');
                    if (parentIdIndex >= 0) {
                        apiParams.filter[parentIdIndex].value = params.parentId;
                    }

                    // Find and update the root_parent_id filter
                    const rootParentIdIndex = apiParams.filter.findIndex((f) => f.property === 'root_parent_id');
                    if (rootParentIdIndex >= 0) {
                        apiParams.filter[rootParentIdIndex].value = params.rootParentId;
                    }
                }

                // Log the complete API params including sort parameter if present
                console.log('Fetching knowledge listing with params:', {
                    ...apiParams,
                    sort: apiParams.sort ? apiParams.sort : 'no sort specified',
                });

                const response = await knowledgeAPI.fetchKnowledgeListing(apiParams);
                console.log('Successfully fetched items from API:', response.items.length);
                // Update both items and currentList
                this.items = response.items.map((item) => ({ ...item }));
                this.currentList = response.items.map((item) => ({ ...item }));
                this.totalCount = response.totalCount;

                return response.items;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch knowledge listing';
                console.error('Error fetching knowledge listing:', err);
                throw err;
            } finally {
                this.loadingContent = false;
            }
        },

        addItem(item: KnowledgeItem) {
            this.items.push(item);
        },

        async updateRevisionToDraft(kbId: string, updates: Partial<KnowledgeItem>, revisionId: string) {
            const index = this.items.findIndex((item) => item.id === kbId);
            if (index !== -1) {
                this.items[index].status = 'DRAFT';
            }
            // if (updates.content) {
            await this.saveItemRevision(kbId, updates.content ?? '', revisionId, true);
            // }
        },

        async publishRevision(kbId: string, updates: Partial<KnowledgeItem>, revisionId: string) {
            const index = this.items.findIndex((item) => item.id === kbId);
            if (index !== -1) {
                this.items[index].status = 'PUBLISHED';
            }
            //            if (updates.content) {
            await this.saveItemRevision(kbId, updates.content ?? '', revisionId, false, true);
            //           }
        },

        async updateItem(kbId: string, updates: Partial<KnowledgeItem>, revisionId: string, force: boolean = false) {
            const index = this.items.findIndex((item) => item.id === kbId);
            if (index !== -1 || force) {
                this.items[index] = {
                    ...this.items[index],
                    ...updates,
                };

                // If content is being updated, save it as a revision
                if (updates.content) {
                    return await this.saveItemRevision(
                        kbId,
                        updates.content,
                        revisionId,
                        false,
                        false,
                        updates.title,
                        updates.sub_title
                    );
                }
            }
        },

        async saveItemRevision(
            kbId: string,
            body: string,
            revisionId: string,
            draft: boolean = false,
            publish: boolean = false,
            title?: string,
            sub_title?: string
        ) {
            try {
                console.log('Saving revision for item:', kbId, revisionId ? `with revision ID: ${revisionId}` : '');
                const revision: KnowledgeRevision & { title?: string; sub_title?: string } = {
                    id: revisionId,
                    body: '<bt/>' + body,
                };
                if (title) {
                    revision.title = title;
                }
                if (sub_title) {
                    revision.sub_title = sub_title;
                }
                if (draft) {
                    revision['_copy_revision'] = true;
                }
                if (publish) {
                    revision['_publish'] = true;
                }

                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.putRevisions([revision]);
                console.log('Successfully saved revision:', response);
                return response;
            } catch (err) {
                console.error('Error saving revision:', err);
                this.error = err instanceof Error ? err.message : 'Failed to save revision';
                // We don't throw the error here to prevent blocking the UI update
                // The error is logged and stored in the error state
            }
        },

        setSearchQuery(query: string) {
            this.searchQuery = query;
        },

        setStatusFilter(status: string) {
            this.statusFilter = status;
        },

        deleteItem(id: string) {
            const index = this.items.findIndex((item) => item.id === id);
            if (index !== -1) {
                this.items.splice(index, 1);
            }
        },

        async fetchArticleById(id: string) {
            this.loading = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const apiParams: ListingParams = {
                    page: 1,
                    start: 0,
                    limit: 1,
                    filter: [{ property: 'id', value: id }],
                };

                console.log('Fetching article by ID:', id);
                const response = await knowledgeAPI.fetchKnowledgeListing(apiParams);

                const article = response.items.find((article: KnowledgeItem) => article.id === id);
                if (article) {
                    console.log('Successfully fetched article by ID:', id);
                    this.currentArticle = article;
                    
                    return this.currentArticle;
                } else {
                    this.error = `Article with ID ${id} not found`;
                    throw new Error(this.error);
                }
            } catch (err) {
                this.error = err instanceof Error ? err.message : `Failed to fetch article with ID ${id}`;
                console.error('Error fetching article by ID:', err);
                throw err;
            } finally {
                this.loading = false;
            }
        },

        clearCurrentArticle() {
            this.currentArticle = null;
        },

        clearCurrentList() {
            this.currentList = [];
            this.currentRootId = null;
        },

        async fetchLibraries() {
            this.loadingLibraries = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const libraries = await knowledgeAPI.fetchKnowledgeLibraries();
                this.libraries = libraries;

                // If we have libraries and no selected library, select the first one
                if (libraries.length > 0 && !this.selectedLibrary) {
                    this.selectedLibrary = libraries[0];
                }

                return libraries;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch knowledge libraries';
                console.error('Error fetching knowledge libraries:', err);
                throw err;
            } finally {
                this.loadingLibraries = false;
            }
        },

        // Load libraries without any side effects on the current list
        async fetchLibrariesOnly() {
            this.loadingLibraries = true;
            this.error = null;

            try {
                // Don't set the error state to avoid triggering watchers
                const knowledgeAPI = useKnowledgeAPI();
                const libraries = await knowledgeAPI.fetchKnowledgeLibraries();
                this.libraries = libraries;

                // If we have libraries and no selected library, select the first one
                if (libraries.length > 0 && !this.selectedLibrary) {
                    this.selectedLibrary = libraries[0];
                }

                return libraries;
            } catch (err) {
                console.error('Error fetching knowledge libraries:', err);
                // Return an empty array instead of throwing
                return [];
            } finally {
                this.loadingLibraries = false;
            }
        },

        setSelectedLibrary(library: KnowledgeLibrary) {
            this.selectedLibrary = library;
        },

        setCurrentArticle(article: KnowledgeItem) {
            this.currentArticle = article;
        },

        async createArticle(title: string, libraryId: string) {
            this.loading = true;
            this.error = null;

            try {
                // If using the default fallback library ID, try to use the first real library or a hardcoded one
                let targetLibraryId = libraryId;
                if (libraryId === 'default') {
                    // If we have libraries in the store, use the first one
                    if (this.libraries && this.libraries.length > 0) {
                        targetLibraryId = this.libraries[0].id;
                        console.log('Using first available library:', targetLibraryId);
                    } else {
                        // Otherwise use the current root ID if available (meaning we're in a library)
                        if (this.currentRootId) {
                            targetLibraryId = this.currentRootId;
                            console.log('Using current root ID as library:', targetLibraryId);
                        } else {
                            console.warn('No library ID available, article creation might fail');
                            // We'll try with the provided default ID anyway
                        }
                    }
                }

                // Create the article data payload
                const articleData = {
                    title: title,
                    root_parent_id: targetLibraryId,
                    short_name: title.toLowerCase().replace(/\s+/g, '-'),
                    id: 'boomtown.kb.model.Article-1', // This will be replaced by server
                    owner_partner_id: '',
                    partner_ids: [],
                    internal_team_ids: [],
                    sub_title: '',
                    body: '',
                    saved_reply: '',
                    visibility: 1,
                    searchable: true,
                    status: 0, // Published
                    type: 0,
                    updated: '',
                    created: '',
                    _merge: '',
                    merge_ids: '',
                    url: '',
                    url_avatar: '',
                    bc__tags_object_kb: [],
                    bc__tags_positions_object_kb: [],
                    c__contributor_user_ids: [],
                    _latestDraftId: '',
                    auto_gen_articles: false,
                    bc__tags_object_kb_labels: [],
                    c__org_names: [],
                    bc__tags_object_members_devices_dict: [],
                    c__tech_labels: [],
                    bc__tags_support: [],
                    c__support_labels: [],
                    editor_user_ids: [],
                    c__editor_user_names: [],
                    keywords: '',
                    url_login: '',
                    _canWrite: true,
                    _uiAccess: {
                        edit: true,
                        merge: true,
                        clone: true,
                        delete: true,
                    },
                };

                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.createKnowledgeArticle(articleData);

                // Refresh the current list to show the new article
                await this.fetchKnowledgeListing({
                    rootParentId: this.currentRootId,
                    parentId: null,
                });

                return response;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to create article';
                console.error('Error creating article:', err);
                throw err;
            } finally {
                this.loading = false;
            }
        },

        async cloneArticle(articleId: string) {
            this.loading = true;
            this.error = null;

            try {
                // First, fetch the article to be cloned
                const sourceArticle = await this.fetchArticleById(articleId);

                if (!sourceArticle) {
                    throw new Error(`Could not find article with ID ${articleId} to clone`);
                }

                console.log('Cloning article:', sourceArticle.title, 'ID:', articleId);

                // Create a base article data structure
                const clonedArticleData: Record<string, any> = {
                    title: `${sourceArticle.title} (Copy)`,
                    short_name: `${sourceArticle.title} (Copy)`.toLowerCase().replace(/\s+/g, '-'),
                    id: 'boomtown.kb.model.Article-1', // This will be replaced by server
                    sub_title: '',
                    body: sourceArticle.body || sourceArticle.content || '',
                    saved_reply: '',
                    visibility: 1,
                    searchable: true,
                    status: 1, // Set as draft initially
                    type: 0,
                    updated: '',
                    created: '',
                    _merge: '',
                    merge_ids: '',
                    url: '',
                    url_avatar: '',
                    bc__tags_object_kb: [],
                    bc__tags_positions_object_kb: [],
                    c__contributor_user_ids: [],
                    _latestDraftId: '',
                    auto_gen_articles: false,
                    bc__tags_object_kb_labels: [],
                    c__org_names: [],
                    bc__tags_object_members_devices_dict: [],
                    c__tech_labels: [],
                    bc__tags_support: [],
                    c__support_labels: [],
                    editor_user_ids: [],
                    c__editor_user_names: [],
                    keywords: '',
                    url_login: '',
                    _canWrite: true,
                    _uiAccess: {
                        edit: true,
                        merge: true,
                        clone: true,
                        delete: true,
                    },
                };

                // Copy properties from source article if they exist
                const propertiesToCopy = [
                    'root_parent_id',
                    'owner_partner_id',
                    'partner_ids',
                    'internal_team_ids',
                    'sub_title',
                    'visibility',
                    'searchable',
                    'url',
                    'url_avatar',
                    'bc__tags_object_kb',
                    'bc__tags_positions_object_kb',
                    'c__contributor_user_ids',
                    'auto_gen_articles',
                    'bc__tags_object_kb_labels',
                    'c__org_names',
                    'bc__tags_object_members_devices_dict',
                    'c__tech_labels',
                    'bc__tags_support',
                    'c__support_labels',
                    'editor_user_ids',
                    'c__editor_user_names',
                    'keywords',
                    'url_login',
                ];

                // Copy properties only if they exist in the source article
                for (const prop of propertiesToCopy) {
                    if (prop in sourceArticle) {
                        clonedArticleData[prop] = sourceArticle[prop as keyof typeof sourceArticle];
                    }
                }

                // Special handling for metadata if it exists
                if (sourceArticle.metadata) {
                    for (const key in sourceArticle.metadata) {
                        const metaKey = `c__${key}`;
                        // Use a type assertion to avoid the index signature error
                        clonedArticleData[metaKey] = (sourceArticle.metadata as Record<string, any>)[key];
                    }
                }

                console.log('Cloned article data:', clonedArticleData);

                // Create the cloned article
                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.createKnowledgeArticle(clonedArticleData);

                // Refresh the current list to show the new article
                await this.fetchKnowledgeListing({
                    rootParentId: this.currentRootId,
                    parentId: null,
                });

                return response;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to clone article';
                console.error('Error cloning article:', err);
                throw err;
            } finally {
                this.loading = false;
            }
        },

        async archiveArticle(articleId: string) {
            this.loading = true;
            this.error = null;

            try {
                console.log('Archiving article:', articleId);

                // Call the API to archive the article
                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.archiveArticle(articleId);

                // Refresh the current list to reflect changes
                await this.fetchKnowledgeListing({
                    rootParentId: this.currentRootId,
                    parentId: null,
                });

                return response;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to archive article';
                console.error('Error archiving article:', err);
                throw err;
            } finally {
                this.loading = false;
            }
        },

        async deleteArticle(articleId: string) {
            this.loading = true;
            this.error = null;

            try {
                console.log('Deleting article:', articleId);

                // Call the API to delete the article
                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.deleteArticle(articleId);

                // Refresh the current list to reflect changes
                await this.fetchKnowledgeListing({
                    rootParentId: this.currentRootId,
                    parentId: null,
                });

                return response;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to delete article';
                console.error('Error deleting article:', err);
                throw err;
            } finally {
                this.loading = false;
            }
        },

        async fetchTeams() {
            this.loadingTeams = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const teams = await knowledgeAPI.fetchTeams();
                this.teams = teams;
                return teams;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch teams';
                console.error('Error fetching teams:', err);
                throw err;
            } finally {
                this.loadingTeams = false;
            }
        },

        // Load teams without any side effects
        async fetchTeamsOnly() {
            this.loadingTeams = true;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const teams = await knowledgeAPI.fetchTeams();
                this.teams = teams;
                return teams;
            } catch (err) {
                console.error('Error fetching teams:', err);
                // Return an empty array instead of throwing
                return [];
            } finally {
                this.loadingTeams = false;
            }
        },

        async fetchAvailableKnowledgeBases() {
            this.loadingKnowledgeBases = true;
            this.error = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const kbs = await knowledgeAPI.fetchAvailableKnowledgeBases();
                this.availableKnowledgeBases = kbs;
                return kbs;
            } catch (err) {
                this.error = err instanceof Error ? err.message : 'Failed to fetch available knowledge bases';
                console.error('Error fetching available knowledge bases:', err);
                throw err;
            } finally {
                this.loadingKnowledgeBases = false;
            }
        },

        // Add a method to fetch without affecting error state
        async fetchAvailableKnowledgeBasesOnly() {
            this.loadingKnowledgeBases = true;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const kbs = await knowledgeAPI.fetchAvailableKnowledgeBases();
                this.availableKnowledgeBases = kbs;
                return kbs;
            } catch (err) {
                console.error('Error fetching available knowledge bases:', err);
                return [];
            } finally {
                this.loadingKnowledgeBases = false;
            }
        },

        async fetchLabelTree() {
            try {
                // Fetch the root label tree (like in KnowledgeSidePanel)
                const knowledgeAPI = useKnowledgeAPI();
                const tree = await knowledgeAPI.fetchKnowledgeTree();
                this.labelTree = tree;
                // Flatten the tree to a map of id -> text
                const map: Record<string, string> = {};
                function flatten(nodes: KnowledgeNode[]) {
                    for (const node of nodes) {
                        map[node.id] = node.text;
                        if (node.children) flatten(node.children);
                    }
                }
                flatten(tree);
                this.labelMap = map;
                return tree;
            } catch (err) {
                console.error('Error fetching label tree:', err);
                this.labelTree = [];
                this.labelMap = {};
                return [];
            }
        },

        async updateArticleField(revisionId: string, field: string, value: any) {
            try {
                const knowledgeAPI = useKnowledgeAPI();
                const revision: KnowledgeRevision & Record<string, any> = { id: revisionId };
                revision[field] = value;
                const response = await knowledgeAPI.putRevisions([revision]);
                return response;
            } catch (error) {
                console.error('Error updating article field:', error);
                throw error;
            }
        },

        async fetchComments(params: ListingParams = {}) {
            this.loadingComments = true;
            this.commentsError = null;

            try {
                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.fetchKnowledgeComments(params);
                
                // Update comments and total count
                if (response.data?.items) {
                    this.comments = response.data.items;
                    this.totalCommentsCount = response.data.totalCount;
                } else if (response.kb_log?.results) {
                    this.comments = response.kb_log.results;
                    this.totalCommentsCount = response.kb_log.totalCount;
                }

                return this.comments;
            } catch (err) {
                this.commentsError = err instanceof Error ? err.message : 'Failed to fetch comments';
                console.error('Error fetching comments:', err);
                throw err;
            } finally {
                this.loadingComments = false;
            }
        },

        async resolveComment(commentId: string) {
            try {
                const knowledgeAPI = useKnowledgeAPI();
                const response = await knowledgeAPI.resolveComment(commentId);

                // Remove the resolved comment from the list
                this.comments = this.comments.filter(comment => comment.id !== commentId);
                this.totalCommentsCount--;

                return response;
            } catch (err) {
                console.error('Error resolving comment:', err);
                throw err;
            }
        },
    },
});
