<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoTreeSelect from '@services/ui-component-library/components/BravoTreeSelect.vue';
import { useI18n } from 'vue-i18n';
import { useKnowledgeAPI } from '@/composables/services';

interface LibraryResponse {
  success: boolean;
  pl__kb_libraries: Array<{
    id: string;
    val: string;
    lbl: string;
    sub_title: string | null;
    short_name: string;
    description: string | null;
    owner_partner_id: string;
    url: string;
  }>;
  current_server_time: string;
}

interface LabelResponse {
  success: boolean;
  pl__kb_labels: Array<{
    id: string;
    val: string;
    lbl: string;
    path: string;
    actual_path: string;
    indent_lbl: string;
    root_kb_id: string;
    iconCls: string;
    icon: string | null;
    icon_url: string;
    icon_alt_text: string | null;
    icon_visible: boolean;
    url: string;
  }>;
  current_server_time: string;
}

const { t } = useI18n();

const props = defineProps<{
  visible: boolean;
  node: any;
  nodes: any[];
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh'): void;
}>();

const newLabelName = ref('');
const selectedNode = ref<{ id: string } | null>(null);
const treeData = ref<any[]>([]);
const expandedKeys = ref<Record<string, boolean>>({});
const isLoadingTree = ref(false);

// Create a knowledgeAPI instance
const knowledgeAPI = useKnowledgeAPI();

const handleSaveNewLabel = async () => {
  const response = await knowledgeAPI.addLabel([{
    parent_id:  selectedNode.value ? Object.keys(selectedNode.value)[0] : null,
    root_kb_id:props.node.data.rootParentId,
    id:crypto.randomUUID(),
    parentId:null,
    leaf:false,
    owner_partner_id: '',
    short_name: '',
    title:newLabelName.value,
    sub_title: '',
    status: 0,
    updated: '',
    created: '',
    _merge: '',
    merge_ids: '',
    url: ''
  }]);
  
  if (response.success) {
    emit('update:visible', false);
    emit('refresh');
  } else {
    console.error('Error adding label:', response);
  }
};

watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadLabels();
  }
  else {
    treeData.value = [];
    newLabelName.value = '';
    selectedNode.value = null;
  }
});

function createLibraryTree(librariesData: LibraryResponse, labelsData: LabelResponse) {
  const libraries = librariesData?.pl__kb_libraries || [];
  const labels = labelsData?.pl__kb_labels || [];

  // Group labels by their root_kb_id
  const labelGroups: Record<string, any[]> = {};
  for (const label of labels) {
    const libraryId = label.root_kb_id;
    if (!labelGroups[libraryId]) {
      labelGroups[libraryId] = [];
    }
    labelGroups[libraryId].push(label);
  }

  function buildLabelTree(flatLabels: any[], rootParentId: string) {
    const root: any[] = [];

    for (const label of flatLabels) {
      const parts = label.path.split('/');
      let currentLevel = root;
      let fullPath = '';
      let parentId = rootParentId;

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        fullPath = fullPath ? `${fullPath}/${part}` : part;

        let existing = currentLevel.find(item => item.label === part);
        if (!existing) {
          const isLeaf = i === parts.length - 1;
          const labelData = isLeaf ? label : { id: crypto.randomUUID() }; // Dummy ID for intermediate nodes

          existing = {
            key: labelData.id,
            label: part,
            data: {
              id: labelData.id,
              parentId,
              rootParentId,
              ...(label.root_kb_id ? { root_kb_id: label.root_kb_id } : {})
            },
            leaf: isLeaf && (label.article_cnt === 0),
            icon: "pi pi-tag",
            children: [],
            expanded: true
          };

          currentLevel.push(existing);
        }

        parentId = existing.key;
        currentLevel = existing.children;
      }
    }
    
    return root;
  }

  const tree = libraries.map(library => {
    const labelTree = buildLabelTree(labelGroups[library.id] || [], library.id);

    const data = {
      key: library.id,
      label: library.lbl,
      data: {
        id: library.id,
        parentId: null,
        rootParentId: library.id,
        isLibrary: true,
      },
      leaf: false,
      icon: "pi pi-book",
      children: labelTree.length ? labelTree : null,
      expanded: true
    };

    return data;
  });

  return tree;
}

const loadLabels = async () => {
  if (!props.node) return;

  try {
    isLoadingTree.value = true;
    // Call the libraries API
    let query = {
        sAction: 'metaKBLibraries',
        query: JSON.stringify([{"property":"current_library_id","value":"_no_filter_"},""]),
      };
    const librariesResponse = await knowledgeAPI.loadLabels(query);

    query = {
        sAction: 'metaKBLabels',
        filter: JSON.stringify([{
          property: 'root_kb_id',
          value: props.node.data.rootParentId
        },{
            property: 'excludeId',
            value: ''
        },{
            property: 'excludeChildrenId',
            value: true
        }]),
      };

    const labelsResponse = await knowledgeAPI.loadLabels(query);
    const parsedTreeData = createLibraryTree(librariesResponse, labelsResponse);

    const data = [];

    for (const node of parsedTreeData) {
      if (node.children && node.children.length) {
        data.push(node);
      }
    }

    cleanChildren(data);

    treeData.value = data;

  } catch (error) {
    console.error('Error loading labels:', error);
  } finally {
    isLoadingTree.value = false;
  }
};

const cleanChildren = (data: any) => {
  for (const node of data) {
    if (node.children && node.children.length) {
      cleanChildren(node.children);
    } else {
      delete node.children;
      delete node.icon;
      node.leaf = true;
    }
  }
  return data;
}


</script>

<template>
  <BravoDialog 
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    modal 
    :header="`${t('knowledge.add_label.title')}`"
    :style="{ width: '400px' }"
    :closable="true"
  >
    <div class="edit-label-form">
      <div class="form-field">
        <label for="newLabelName">{{ t('knowledge.add_label.label_name') }} <span class="required">*</span></label>
        <BravoInputText 
          id="newLabelName"
          v-model="newLabelName" 
          :placeholder="t('knowledge.add_label.label_name')"
          class="w-full"
        />
      </div>
      
      <div class="form-field">
        <label for="nestUnder">{{ t('knowledge.add_label.nest_under') }}</label>
        <BravoTreeSelect
          id="nestUnder"
          v-model="selectedNode"
          :options="treeData"
          optionLabel="label"
          :placeholder="t('knowledge.add_label.nest_under')"
          class="w-full"
          dataTestId="add-label-nest-under"
          :expandedKeys="expandedKeys"
          :loading="isLoadingTree"
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <BravoButton 
          class="p-button p-button-text" 
          @click="$emit('update:visible', false)"
          :label="t('common.cancel')"
        >
        </BravoButton>
        <BravoButton 
          class="p-button p-button-primary" 
          @click="handleSaveNewLabel"
          :disabled="!newLabelName"
          :label="t('common.save')"
        >
        </BravoButton>
      </div>
    </template>
  </BravoDialog>
</template>

<style scoped>
.edit-label-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-weight: 500;
  color: var(--text-color);
}

.required {
  color: var(--red-500);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding-top: 1rem;
}
</style> 