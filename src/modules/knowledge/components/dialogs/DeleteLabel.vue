<script setup lang="ts">
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import { useI18n } from 'vue-i18n';
import { useKnowledgeAPI } from '@/composables/services';

const { t } = useI18n();

const props = defineProps<{
  visible: boolean;
  node: any;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'refresh'): void;
}>();

const handleDelete = async () => {
  const knowledgeAPI = useKnowledgeAPI();
  const response = await knowledgeAPI.deleteLabel([{id: props.node.data.id}]);
  if (response.success) {
    emit('update:visible', false);
    emit('refresh');
  } else {
    console.error('Error deleting label:', response);
  }
};
</script>

<template>
  <BravoDialog 
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    modal 
    :header="`${t('Delete Label')}`"
    :style="{ width: '400px' }"
    :closable="true"
    class="knowledge-dialog"
  >
    <div class="delete-confirmation-content">
      <p class="confirmation-message">
        {{ t('Are you sure you want to delete label ') }} <b>{{ node?.label }} ?</b>
      </p>
      <p class="sub-message">
        {{ t('Any sub-labels under it will also be deleted.') }}
      </p>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <BravoButton 
          class="p-button p-button-text" 
          @click="$emit('update:visible', false)"
          :label="t('common.cancel')"
          severity="secondary"
        />
        <BravoButton 
          class="p-button" 
          @click="handleDelete"
          :label="t('common.delete')"
        />
      </div>
    </template>
  </BravoDialog>
</template>