<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import { useToast } from 'primevue/usetoast';
import { knowledgeAPI } from '../../../../services/KnowledgeAPI';

const toast = useToast();
const isLoading = ref(false);

const props = defineProps<{
  visible: boolean;
  selectedAccessLevel?: string;
  teamAccessTo?: string[];
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'save', data: { accessLevel: string; organizations: string[] }): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);
const selectedAccessLevel = ref(props.selectedAccessLevel || '');
const teamAccessTo = ref(props.teamAccessTo || []);
const teams = ref([]);

// Access level options
const accessLevelOptions = [
  { label: 'Public', value: 'public' },
  { label: 'Internal', value: 'internal' },
  { label: 'Private', value: 'private' }
];

// Watch for changes in props.visible
watch(() => props.visible, async (newValue) => {
  isVisible.value = newValue;

  if (newValue) {
    const response = await knowledgeAPI.loadPartnersTeams({ sAction: 'metaPartnersTeams' });
    teams.value = response.pl__org_partners_teams || [];
  }
});

// Watch for changes in isVisible
watch(() => isVisible.value, (newValue) => {
  emit('update:visible', newValue);
});

const onHide = () => {
  isVisible.value = false;
};

const onSave = () => {
  emit('save', {
    accessLevel: selectedAccessLevel.value,
    organizations: teamAccessTo.value
  });
  onHide();
};
</script>

<template>
  <BravoDialog
    v-model:visible="isVisible"
    :header="t('knowledge.actionsMenu.update_access_controls')"
    :modal="true"
    class="knowledge-dialog"
    @hide="onHide"
  >
    <div class="dialog-content">
      <div class="form-section">
        <div class="form-field">
          <label>
            Access Level
            <span class="required">*</span>
          </label>
          <BravoSelect
            id="access-level-select"
            v-model="selectedAccessLevel"
            :options="accessLevelOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Select access level"
            class="w-full"
            dataTestId="access-level-select"
          />
        </div>

        <div class="form-field">
          <label>Restrict Team access to</label>
          <BravoMultiSelect
            v-model="teamAccessTo"
            :options="teams"
            optionLabel="lbl"
            optionValue="val"
            display="chip"
            placeholder="Select organizations"
            class="w-full"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <BravoButton
        :label="t('common.cancel')"
        severity="secondary"
        @click="onHide"
      />
      <BravoButton
        :label="t('knowledge.actionsMenu.update')"
        @click="onSave"
      />
    </template>
  </BravoDialog>
</template>

<style>
@import './dialogs.css';
</style> 