 <script setup lang="ts">
  import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
  import BravoPopover from '@services/ui-component-library/components/BravoPopover.vue';
  import BravoCheckbox from '@services/ui-component-library/components/BravoCheckbox.vue';
  import { ref, watch, watchEffect } from 'vue';

// Define filter keys as a type for type safety
type FilterKey = 'all' | 'standard' | 'articleUpdated' | 'statusChanged' | 'articleShared' |
  'ratingSubmitted' | 'commentAdded' | 'articleCreated' | 'articleUsed' |
  'articleViewed' | 'appearedInSearch';

// Define the filters interface
interface Filters {
  all: boolean;
  standard: boolean;
  articleUpdated: boolean;
  statusChanged: boolean;
  articleShared: boolean;
  ratingSubmitted: boolean;
  commentAdded: boolean;
  articleCreated: boolean;
  articleUsed: boolean;
  articleViewed: boolean;
  appearedInSearch: boolean;
  [key: string]: boolean; // Index signature to allow string indexing
}

const filterBtnTarget = ref<HTMLElement | null>(null);
const filterPopover = ref<InstanceType<typeof BravoPopover> | null>(null);
const filters = ref<Filters>({
  all: true,
  standard: false,
  articleUpdated: true,
  statusChanged: true,
  articleShared: true,
  ratingSubmitted: true,
  commentAdded: true,
  articleCreated: true,
  articleUsed: true,
  articleViewed: true,
  appearedInSearch: true,
});

const updateKeys: FilterKey[] = [
  'articleUpdated',
  'statusChanged',
  'articleShared',
  'ratingSubmitted',
  'commentAdded',
  'articleCreated',
  'articleUsed',
  'articleViewed',
  'appearedInSearch',
];

// Define the option interface
interface OptionItem {
  id: string;
  label: string;
  filterOption: string[];
}

// Define the options list type
interface OptionList {
  [key: number]: OptionItem;
}

// List of all options
const optionList: OptionList = {
  1: { id: 'type_update', label: 'Updated', filterOption: ['articleUpdated'] },
  2: { id: 'type_accessed', label: 'Accessed', filterOption: ['articleViewed'] },
  4: { id: 'type_note', label: 'Comment Resolved', filterOption: ['commentAdded', 'articleUpdated'] },
  5: { id: 'type_create', label: 'Created', filterOption: ['articleCreated', 'articleUpdated'] },
  6: { id: 'type_archive', label: 'Archived', filterOption: [] },
  7: { id: 'type_search', label: 'Searched', filterOption: ['appearedInSearch'] },
  8: { id: 'type_rate_up', label: 'Rate Up', filterOption: ['ratingSubmitted'] },
  9: { id: 'type_rate_down', label: 'Rate Down', filterOption: ['ratingSubmitted'] },
  10: { id: 'type_lbl_update', label: 'Label Update', filterOption: [] },
  13: { id: 'type_lbl_create', label: 'Label Create', filterOption: [] },
  14: { id: 'type_lbl_archive', label: 'Label Archive', filterOption: [] },
  15: { id: 'type_library_create', label: 'Library Created', filterOption: [] },
  16: { id: 'type_library_update', label: 'Library Update', filterOption: [] },
  17: { id: 'type_rev_update', label: 'Revision Updated', filterOption: [] },
  18: { id: 'type_rev_create', label: 'Revision Created', filterOption: [] },
  19: { id: 'type_rev_archive', label: 'Revision Archived', filterOption: [] },
  22: { id: 'type_comment', label: 'Comment', filterOption: ['commentAdded'] },
  23: { id: 'type_published', label: 'Published', filterOption: ['articleUpdated'] },
  24: { id: 'type_unpublished', label: 'Unpublished', filterOption: ['articleUpdated'] },
  25: { id: 'type_reassigned', label: 'Reassigned', filterOption: ['articleUpdated', 'revisionReassigned'] },
};

const emit = defineEmits(['update:filters', 'popover:hide', 'popover:show']);

// Watch "All" checkbox
watch(() => filters.value.all, (newVal) => {
  if (newVal) {
    filters.value.standard = false;
    updateKeys.forEach(key => filters.value[key] = true);
  }
});

watch(() => filters.value.standard, (newVal) => {
  if (newVal) {
    filters.value.all = false;
    updateKeys.forEach((key, index) => {
      filters.value[key] = index < 6; // first 6 true
    });
  }
});

watchEffect(() => {
  if (!filters.value.all && !filters.value.standard) {
    updateKeys.forEach(key => filters.value[key] = false);
  }
});

// Match checked filters to optionList
watchEffect(() => {
  const checkedKeys = updateKeys.filter(key => filters.value[key]);
  const matchedOptionIndexes: number[] = [];

  Object.entries(optionList).forEach(([index, option]) => {
    if (option.filterOption.some((opt: string) => checkedKeys.includes(opt as FilterKey))) {
      matchedOptionIndexes.push(Number(index));
    }
  });

  emit('update:filters', matchedOptionIndexes);
  // use or emit matchedOptionIndexes as needed
});

/**
 * Toggle the filter popover
 * @param event The click event
 */
const toggle = (event: MouseEvent): void => {
    if (filterPopover.value) {
        filterPopover.value.toggle(event);
    }
};

/**
 * Handle popover show event
 */
const onPopoverShow = (): void => {
    emit('popover:show');
};

/**
 * Handle popover hide event
 * This will trigger the filter application
 */
const onPopoverHide = (): void => {
    emit('popover:hide');
};
  </script>

<template>
    <div>
      <BravoButton
            aria-label="Filter events"
            label="Filter"
            icon="pi pi-filter"
            class="filterBtn"
            severity="secondary"
            @click="toggle"
        />

        <BravoPopover
            ref="filterPopover"
            :target="filterBtnTarget"
            dismissable
            position="right"
            @show="onPopoverShow"
            @hide="onPopoverHide"
            style="width: 450px; max-height: 350px; overflow-y: auto;">
            <div class="filter-container">
        <div class="filter-section">
          <h4>Set</h4>
          <BravoCheckbox v-model="filters.all" :withLabel="true" label="All" binary id="checkbox-all" dataTestId="checkbox-all" forElement="all" :isRequired="false" />
          <BravoCheckbox v-model="filters.standard" :withLabel="true" label="Standard" binary id="checkbox-standard" dataTestId="checkbox-standard" forElement="standard" :isRequired="false" />
        </div>

        <div class="filter-section">
          <h4>Update</h4>
          <BravoCheckbox v-model="filters.articleUpdated" :withLabel="true" label="Article Updated" binary id="checkbox-au" dataTestId="checkbox-au" forElement="articleUpdated" :isRequired="false" />
          <BravoCheckbox v-model="filters.statusChanged" :withLabel="true" label="Status Changed" binary id="checkbox-sc" dataTestId="checkbox-sc" forElement="statusChanged" :isRequired="false" />
          <BravoCheckbox v-model="filters.articleShared" :withLabel="true" label="Article Shared" binary id="checkbox-as" dataTestId="checkbox-as" forElement="articleShared" :isRequired="false" />
          <BravoCheckbox v-model="filters.ratingSubmitted" :withLabel="true" label="Rating Submitted" binary id="checkbox-rs" dataTestId="checkbox-rs" forElement="ratingSubmitted" :isRequired="false" />
          <BravoCheckbox v-model="filters.commentAdded" :withLabel="true" label="Comment Added" binary id="checkbox-ca" dataTestId="checkbox-ca" forElement="commentAdded" :isRequired="false" />
          <BravoCheckbox v-model="filters.articleCreated" :withLabel="true" label="Article Created" binary id="checkbox-ac" dataTestId="checkbox-ac" forElement="articleCreated" :isRequired="false" />
          <BravoCheckbox v-model="filters.articleUsed" :withLabel="true" label="Article Used" binary id="checkbox-au2" dataTestId="checkbox-au2" forElement="articleUsed" :isRequired="false" />
          <BravoCheckbox v-model="filters.articleViewed" :withLabel="true" label="Article Viewed" binary id="checkbox-av" dataTestId="checkbox-av" forElement="articleViewed" :isRequired="false" />
          <BravoCheckbox v-model="filters.appearedInSearch" :withLabel="true" label="Appeared in Search" binary id="checkbox-ais" dataTestId="checkbox-ais" forElement="appearedInSearch" :isRequired="false" />
        </div>
      </div>
        </BravoPopover>
    </div>
</template>
  <style scoped>
  .filterBtn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  gap: 60px; /* Increased spacing between the two sections */
  padding: 20px 25px;
  min-width: 400px; /* Optional: make sure the popover has enough width */
  background-color: var(--surface-card);
  border-radius: 0.5rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-section h4 {
  margin: 0 0 10px;
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 1px solid var(--surface-200);
  padding-bottom: 8px;
}
  </style>