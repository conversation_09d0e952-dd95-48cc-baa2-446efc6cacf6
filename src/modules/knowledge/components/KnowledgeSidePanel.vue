<script setup lang="ts">
import { useKnowledgeStore } from '../stores/knowledge';
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import Tree from 'primevue/tree';
import Dropdown from 'primevue/dropdown';
import type { KnowledgeNode } from '../../../services/KnowledgeAPI';
import { useKnowledgeAPI } from '@/composables/services';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTitle2 from '@services/ui-component-library/components/BravoTypography/BravoTitle2.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import Tooltip from 'primevue/tooltip';
import { useI18n } from 'vue-i18n';
import 'primeicons/primeicons.css'
import { useUserStore } from '@/stores/user.ts';
import EditLabelDialog from './dialogs/EditLabel.vue';
import AddLabelDialog from './dialogs/AddLabel.vue';
import DeleteLabelDialog from './dialogs/DeleteLabel.vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';

// Define interfaces for PrimeVue Tree component
interface TreeNode {
  key?: string;
  label?: string;
  data?: any;
  icon?: string;
  children?: TreeNode[];
  leaf?: boolean;
  loading?: boolean;
  selectable?: boolean;
}

interface PrimeVueTreeNode {
  key: string;
  label: string;
  data?: any;
  icon?: string;
  children?: PrimeVueTreeNode[];
  leaf?: boolean;
  loading?: boolean;
  article_cnt?: number;
  isLibrary?: boolean;
  access_level?: string;
  root_kb_id?: string;
}

type TreeSelectionKeys = Record<string, boolean>;

const expandedKeys = ref<Record<string, boolean>>({})
const selectedNodeKey = ref<TreeSelectionKeys>({})
const loading = ref(false)
const isMinimized = ref(false)
const panelWidth = ref(280) // Default width in px
const isResizing = ref(false)
const { t } = useI18n();

const menuRef = ref();
const editDialogVisible = ref(false);
const addSubLabelDialogVisible = ref(false);
const deleteConfirmVisible = ref(false);
const selectedNode = ref<any>(null);
const editLabelName = ref('');
const editNestUnder = ref(null);
const editIcon = ref('folder');
const editAltText = ref('');
const newLabelName = ref('');
const newLabelParent = ref(null);

const userStore = useUserStore();

// Computed property for dynamic menu items based on selected node
const menuItems = computed(() => {
  if (!selectedNode.value) return [];
  
  const items = [];
  const node = selectedNode.value;
  const record = node.data;

  // Common items for all node types
  if (record.uiAccess.edit && !record.isLibrary) {
    items.push({
      label: 'Edit',
      icon: 'pi pi-pencil',
      command: () => {
        showEditDialog(node);
    }
    });
  } 

  const accessLevel = userStore.getUserAccessLevel('kb_labels');
  // Add sub-label option for non-leaf nodes
  if (accessLevel && typeof accessLevel === 'object' && 'add' in accessLevel) {
    items.push({
      label: `Add ${record.isLibrary ? 'Label' : 'Sub-label'}`,
      icon: 'pi pi-plus', 
      command: () => {
        showAddSubLabelDialog(node);
      }
    });
  }

  if (record.uiAccess.delete && !record.isLibrary) {
    items.push({
      label: 'Delete Label',
      icon: 'pi pi-trash',
      command: () => {
        showDeleteConfirm(node);
      }
    });
  }
  
  return items;
});

// Check initial screen width
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

// Clean up event listener
const checkScreenSize = () => {
  isMinimized.value = window.innerWidth <= 800
}

const toggleSidebar = () => {
  isMinimized.value = !isMinimized.value
}

// Event handlers for resizing
const startResize = (event: MouseEvent) => {
  if (isMinimized.value) return
  
  isResizing.value = true
  document.addEventListener('mousemove', resizePanel)
  document.addEventListener('mouseup', stopResize)
  
  // Prevent text selection during resize
  event.preventDefault()
}

const resizePanel = (event: MouseEvent) => {
  if (!isResizing.value) return
  
  // Calculate new width based on mouse position
  // Set minimum and maximum width constraints
  const newWidth = Math.max(280, Math.min(500, event.clientX))
  panelWidth.value = newWidth
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', resizePanel)
  document.removeEventListener('mouseup', stopResize)
}

// Remove event listeners when component is unmounted
onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
  document.removeEventListener('mousemove', resizePanel)
  document.removeEventListener('mouseup', stopResize)
})

const store = useKnowledgeStore()
const libraries = computed(() => store.libraries)
const selectedLibrary = computed({
  get: () => store.selectedLibrary,
  set: (value) => store.selectedLibrary = value
})

// Computed property for store loading and error states
const isLoading = computed(() => store.loading)
const errorMessage = computed(() => store.error)

// Add a ref to track loading nodes
const loadingNodes = ref<Set<string>>(new Set())

// Transform API knowledge nodes to PrimeVue TreeNode format
const transformToPrimeVueNodes = (
  nodes: KnowledgeNode[] = [], 
  parentId: string | null = null,
  rootParentId: string | null = null
): PrimeVueTreeNode[] => {
  return nodes.map((node, index) => {
    // For top-level nodes (no parentId), use their own ID as rootParentId
    // For child nodes, use the passed rootParentId
    const nodeRootParentId = parentId === null ? node.id : rootParentId
    
    const treeNode: PrimeVueTreeNode = {
      key: node.id || String(index),
      label: node.text,
      data: {
        id: node.id,
        parentId: parentId,
        rootParentId: nodeRootParentId,
        article_cnt: node.article_cnt,
        isLibrary: node.isLibrary,
        uiAccess: node._uiAccess,
        root_kb_id: node.root_kb_id,
      },
      leaf: node.leaf,
      icon: parentId === null ? 'pi pi-book' : 'pi pi-tag',
      children: node.children ? transformToPrimeVueNodes(
        node.children, 
        node.id,  // Current node becomes the parent
        nodeRootParentId  // Pass down the root parent
      ) : []
    }
    return treeNode
  })
}

const nodes = computed(() => {
  if (store.treeNodes.length === 0) {
    return []
  }
  return transformToPrimeVueNodes(store.treeNodes)
})

const fetchData = async () => {
  try {
    await store.fetchKnowledgeTree()
  } catch (error) {
    console.error('Failed to fetch knowledge tree:', error)
  }
}

onMounted(() => {
  fetchData()
})

watch(() => selectedLibrary.value, () => {
  fetchData()
})

// Update the nodeSelect handler
const nodeSelect = (event: any) => {
  console.log('nodeSelect called with event:', event)
  
  try {
    let node = null
    
    if (event && event.node) {
      node = event.node
    } else if (typeof event === 'object' && 'data' in event) {
      node = event
    }
    
    if (!node || !node.data) {
      console.error('Node selection error: Invalid node data', event)
      return
    }
    
    console.log('Processing node selection for node:', node)

    // Update the selectedNodeKey in the store
    store.selectedNodeKey = { [node.key]: true }

    // Use the store method and handle the response
    store.fetchKnowledgeListing({
      parentId: event.key === node.data.rootParentId ? null : event.key,
      rootParentId: node.data.rootParentId
    })
      .then(items => console.log('Successfully fetched knowledge listing with', items.length, 'items'))
      .catch(error => console.error('Error fetching knowledge listing:', error))
  } catch (error) {
    console.error('Error in nodeSelect handler:', error)
  }
}

// Update nodeExpand to handle loading state locally without using store loading state
const nodeExpand = async (event: any) => {
  console.log('nodeExpand called with event:', event)
  
  try {
    let node = null
    
    if (event && event.node) {
      node = event.node
    } else if (typeof event === 'object' && 'data' in event) {
      node = event
    }
    
    if (!node || !node.data) {
      console.error('Node expansion error: Invalid node data', event)
      return
    }
    
    // If this is a parent node without children, try to load them
    if (!node.leaf && ((!node.children || node.children.length === 0) || event.force)) {
      try {
        // Set loading state for this node
        loadingNodes.value.add(node.key)
        
        // Call the store method directly without setting global loading state
        const knowledgeAPI = useKnowledgeAPI();
const children = await knowledgeAPI.fetchKnowledgeTree({ node: node.data.id })
        store.updateNodeChildren(store.treeNodes, node.data.id, children)
      } catch (error) {
        console.error('Error expanding node:', error)
      } finally {
        // Remove loading state
        loadingNodes.value.delete(node.key)
      }
    }
  } catch (error) {
    console.error('Error in nodeExpand handler:', error)
  }
}

const showEditDialog = (node: any) => {
  selectedNode.value = node;
  editDialogVisible.value = true;
};

const showAddSubLabelDialog = (node: any) => {
  selectedNode.value = node;
  addSubLabelDialogVisible.value = true;
};

const showDeleteConfirm = (node: any) => {
  selectedNode.value = node;
  deleteConfirmVisible.value = true;
};

const handleRefresh = async (parentId?: string) => {
  if (parentId) {
    // Find the parent node in the tree
    const findAndRefreshNode = (nodes: any[]): boolean => {
      for (const node of nodes) {
        if (node.key === parentId) {
          // Refresh this node's children
          nodeExpand({ node, force: true });
          return true;
        }
        if (node.children && findAndRefreshNode(node.children)) {
          return true;
        }
      }
      return false;
    };

    findAndRefreshNode(nodes.value);
  } else {
    // If no parentId provided, refresh the entire tree
    await fetchData();
  }
};

const handleSaveEdit = async () => {
  editDialogVisible.value = false;
  const knowledgeAPI = useKnowledgeAPI();
const response = await knowledgeAPI.editLabel([{title: editLabelName.value, id: selectedNode.value.data.id}]);
  if (response.success) {
    editDialogVisible.value = false;
    fetchData();
  } else {
    console.error('Error deleting label:', response);
  }
};

const handleDelete = async () => {
  const knowledgeAPI = useKnowledgeAPI();
const response = await knowledgeAPI.deleteLabel([{id: selectedNode.value.data.id}]);
  if (response.success) {
    deleteConfirmVisible.value = false;
    fetchData();
  } else {
    console.error('Error deleting label:', response);
  }
};

const showMenu = (event: MouseEvent, node: any) => {
  selectedNode.value = node;
  menuRef.value?.toggle(event);
  event.stopPropagation();
};

// Handle tree node deselection
const nodeUnselect = () => {
  // Clear the selectedNodeKey in the store
  store.selectedNodeKey = {}
  
  store.fetchKnowledgeListing({
    parentId: null,
    rootParentId: null
  });
}
</script>

<template>
  <div class="side-panel" 
    :class="{ 'minimized': isMinimized, 'resizing': isResizing }" 
    :style="!isMinimized ? { width: `${panelWidth}px` } : {}"
  >
    <div class="panel-header">
      <div class="header-content">
        <BravoTitlePage v-if="!isMinimized">{{ t('knowledge.knowledge') }}</BravoTitlePage>
      </div>
    </div>

    <div class="panel-content" v-if="!isMinimized">
      <div class="libraries-heading">
        <BravoTitle2>{{ t('knowledge.libraries') }}</BravoTitle2>
      </div>
      <div v-if="errorMessage" class="error-state">
        <p>{{ errorMessage }}</p>
        <button @click="fetchData" class="retry-button">
          <i class="pi pi-refresh"></i> Retry
        </button>
      </div>
      <Tree
        v-else
        :value="nodes"
        v-model:expandedKeys="expandedKeys"
        v-model:selectionKeys="selectedNodeKey"
        selectionMode="single"
        class="navigation-tree"
        @nodeSelect="nodeSelect"
        @nodeExpand="nodeExpand"
        @nodeUnselect="nodeUnselect"
      >
      <template #default="slotProps">
          <div class="tree-node-content">
            <span class="node-label" :class="{ 'has-children': !slotProps.node.leaf }">{{ slotProps.node.label }}</span>
          </div>
          <div class="node-actions">
              <i 
                class="pi pi-ellipsis-h node-action-icon" 
                @click="showMenu($event, slotProps.node)"
                v-tooltip.bottom="{
                  value: t('knowledge.actions'),
                  showDelay: 400
                }"
              />
              <span v-if="slotProps.node.data.article_cnt > 0" class="article-count">{{ slotProps.node.data.article_cnt }}</span>
            </div>
        </template>
        <template #nodetoggleicon="{ node }">
          <span class="tree-node-toggler">
            <i :class="[
              'pi',
              loadingNodes.has(node.key) 
                ? 'pi-spin pi-spinner loading-spinner'
                : expandedKeys[node.key] ? 'pi-chevron-down' : 'pi-chevron-right'
            ]" />
          </span>
        </template>
      </Tree>
      <BravoMenu ref="menuRef" :model="menuItems" :popup="true" />
        
        <!-- Edit Label Dialog -->
        <EditLabelDialog
          v-model:visible="editDialogVisible"
          :node="selectedNode"
          :nodes="nodes"
          @refresh="handleRefresh"
        />
        
        <!-- Add Sub-label Dialog -->
        <AddLabelDialog
          v-model:visible="addSubLabelDialogVisible"
          :node="selectedNode"
          :nodes="nodes"
          @refresh="handleRefresh"
        />
        
        <!-- Delete Confirmation Dialog -->
        <DeleteLabelDialog
          v-model:visible="deleteConfirmVisible"
          :node="selectedNode"
          @refresh="handleRefresh"
        />
    </div>
    
    <!-- Minimized icons view -->
    <div class="panel-content minimized-icons" v-if="isMinimized">
      <div v-for="node in nodes" :key="node.key" class="minimized-icon" @click="nodeSelect(node)">
        <i :class="node.icon"></i>
      </div>
    </div>
    
    <!-- Bottom positioned toggle button -->
    <div class="panel-footer">
      <BravoButton
        v-if="!isMinimized"
        icon="pi pi-chevron-left"
        text
        severity="secondary" 
        class="minimize-button"
        @click="toggleSidebar"
        aria-label="Toggle sidebar"
        v-tooltip.bottom="{
          value: t('knowledge.collapse'),
          showDelay: 400
        }"
      />
      <BravoButton
        v-if="isMinimized"
        icon="pi pi-chevron-right"
        text
        severity="secondary" 
        class="minimize-button"
        @click="toggleSidebar"
        aria-label="Toggle sidebar"
        v-tooltip.bottom="{
          value: t('knowledge.expand'),
          showDelay: 400
        }"
      />
    </div>
    
    <!-- Resize handle, only visible when not minimized -->
    <div 
      v-if="!isMinimized" 
      class="resize-handle" 
      @mousedown="startResize"
      title="Drag to resize"
    ></div>
  </div>
</template>

<style scoped>
.side-panel {
  width: 280px;
  background-color: var(--surface-50);
  border-right: 1px solid #e2e8f0;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative; /* For resize handle positioning */
}

.side-panel.resizing {
  transition: none; /* Disable transition during resize */
  user-select: none; /* Prevent text selection during resize */
}

.side-panel.minimized {
  width: 60px;
}

/* Resize handle */
.resize-handle {
  position: absolute;
  top: 0;
  right: -5px; /* Position slightly outside the panel */
  width: 10px; /* Wider area for easier grabbing */
  height: 100%;
  cursor: ew-resize;
  z-index: 100;
}

.resize-handle:hover::after {
  content: '';
  position: absolute;
  top: 0;
  right: 5px; /* Center in the handle area */
  width: 2px;
  height: 100%;
  background-color: var(--surface-300);
  opacity: 0.5;
}

.panel-header {
  padding: 1rem 1.5rem;
  height: 64px;
  display: flex;
  align-items: center;
}

.side-panel.minimized .panel-header {
  padding: 0.5rem;
  justify-content: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.side-panel.minimized .header-content {
  justify-content: center;
}

.minimize-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  z-index: 20;
}

.minimize-button:hover {
  background-color: var(--surface-100) !important;
}

.minimized-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding-top: 0.5rem;
  overflow-y: auto;
}

.minimized-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.minimized-icon i {
  font-size: 1.2rem;
  color: #64748b;
}

.minimized-icon:hover {
  background-color: #cbd5e1;
}

.library-selector {
  padding: 0 1rem 0 1rem;
}

.library-dropdown {
  width: 100%;
}

.library-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.library-option i {
  color: #64748b;
}

.library-option span {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.panel-content {
  padding: 1rem .5rem  ;
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #64748b;
}

.retry-button {
  margin-top: 1rem;
  background-color: #e2e8f0;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #cbd5e1;
}

/* Tree Styles */

.tree-node-toggler {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.loading-spinner {
  font-size: 0.875rem;
  color: #64748b;
}

:deep(.p-tree-node-label) {
  padding-left: .3rem;
  display: flex;
  flex: 1;
}

:deep(.tree-node-content) {
  flex: 1;
  display: flex;
  align-items: center;
}

:deep(.p-tree-node-content) {
  display: flex;
  align-items: center;
}

:deep(.p-tree-node-label .node-actions) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding-right: .3rem;
  padding-top: .3rem;
}

:deep(.node-action-icon) {
  visibility: hidden;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
}

:deep(.node-action-icon:hover) {
  background-color: var(--surface-100);
}

:deep(.p-tree-node-content:hover .node-action-icon) {
  visibility: visible;
}

:deep(.p-tree) {
  border: none;
  background: transparent;
  padding: 0;
}

:deep(.p-tree .p-treenode-content:hover) {
  background: #f1f5f9;
}

:deep(.p-tree-node-content.p-tree-node-selected .p-tree-node-icon) {
  color: var(--icon-color-secondary);
}

:deep(.p-tree-node-content.p-tree-node-selectable:hover) {
  background: white !important;
}

:deep(.p-tree-node-content.p-tree-node-selected) {
  background: white !important;
  font-weight: 600;
  color: var(--surface-850);
}

:deep(.p-tree-node-content:hover) .tree-node-toggler:hover {
  background-color: var(--surface-100);
}

.libraries-heading {
  padding: 0rem .5rem .5rem 1rem;
}

/* Add media query for screens <= 800px */
@media (max-width: 800px) {
  .side-panel:not(.minimized) {
    position: absolute;
    z-index: 10;
    height: 100%;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  }
}

/* In minimized state, keep a space at the top for the button */
.side-panel.minimized .minimized-icons {
  margin-top: 48px;
}

.panel-footer {
  padding: 0.5rem;
  display: flex;
  justify-content: flex-end;
}

.side-panel.minimized .panel-footer {
  justify-content: center;
  padding: 0.5rem 0;
}

/* In minimized state, keep a space at the bottom for the button */
.side-panel.minimized .minimized-icons {
  margin-bottom: 60px;
}
</style>
