<script setup lang="ts">
import { computed, ref, nextTick, watch, onMounted, onUnmounted } from 'vue';
import BravoAccordionPanel from '@services/ui-component-library/components/BravoAccordionPanel.vue';
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue';
import BravoAccordionContent from '@services/ui-component-library/components/BravoAccordionContent.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import Skeleton from 'primevue/skeleton';
import Dropdown from 'primevue/dropdown';
import MultiSelect from 'primevue/multiselect';
import { useI18n } from 'vue-i18n';
import { extractPathname, formatDate } from '../utils/formatHelpers';
import { useKnowledgeStore } from '../stores/knowledge';
import { useMetaStore } from '@/stores/meta';
import { usePartnerStore } from '@/stores/partner';
import { knowledgeAPI } from '@/services/KnowledgeAPI';
import ToggleSwitch from 'primevue/toggleswitch';
import { storeToRefs } from 'pinia';
import Chip from 'primevue/chip';
import Button from 'primevue/button';
import TreeSelect from 'primevue/treeselect';
import { useToast } from 'primevue/usetoast';
import ProgressSpinner from 'primevue/progressspinner';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import TagFormInput from './TagFormInput.vue';
import ProductFormInput from './ProductFormInput.vue';
import SearchTermsFormInput from './SearchTermsFormInput.vue';

const { t } = useI18n();
const knowledgeStore = useKnowledgeStore();
const metaStore = useMetaStore();
const partnerStore = usePartnerStore();
const { availableKnowledgeBases, loadingKnowledgeBases, labelTree, labelMap } = storeToRefs(knowledgeStore);
const toast = useToast();

// Define props and emits
const props = defineProps<{
    article: any;
    isLoading?: boolean;
    isWideMode?: boolean;
    isReady: boolean;
}>();

const emit = defineEmits<{
    (e: 'update:field', field: string, value: any): void;
    (e: 'update', article: any): void;
}>();

// State for editable fields
const isOwnerEditing = ref(false);
const editedOwner = ref('');
const isUrlEditing = ref(false);
const editedUrl = ref('');
const isVisibilityEditing = ref(false);
const editedVisibility = ref<number | null>(null);
const isTeamAccessEditing = ref(false);
const editedTeamAccess = ref<string[]>([]);
const isLibraryEditing = ref(false);
const editedLibrary = ref('');
const isOrgAccessEditing = ref(false);
const editedOrgAccess = ref<Array<string | number>>([]);
const isProductsEditing = ref(false);
const editedProducts = ref<string[]>([]);
const isTagsEditing = ref(false);
const editedTags = ref<string[]>([]);
const isSearchTermsEditing = ref(false);
const editedSearchTerms = ref<string[]>([]);
const isRelatedArticlesEditing = ref(false);
const editedAutoGenArticles = ref(false);
const editedRelatedArticles = ref<string[]>([]);
const editedLabels = ref<string[]>([]);

// Reference to the input elements
const urlInput = ref<HTMLInputElement | null>(null);
const visibilityInput = ref<any>(null);
const teamAccessInput = ref<any>(null);
const libraryInput = ref<any>(null);
const orgAccessInput = ref<any>(null);
const ownerInput = ref<any>(null);
const productsInput = ref<any>(null);
const tagsInput = ref<any>(null);
const searchTermsInput = ref<any>(null);

// Visibility options
const visibilityOptions = [
    { label: 'Public', value: 0 },
    { label: 'Internal', value: 1 },
    { label: 'Partner Ecosystem', value: 2 }
];

// Map visibility value to the article's visibility property
const getVisibilityValueFromArticle = (article: any) => {
    if (article.c__d_visibility === 'Internal' || article.isPrivate) {
        return 1;
    } else if (article.c__d_visibility === 'Partner Ecosystem') {
        return 2;
    }
    return 0; // Public by default
};

// Map visibility value to label
const getVisibilityLabel = (value: number) => {
    const option = visibilityOptions.find(opt => opt.value === value);
    return option ? option.label : 'Public';
};

// Watch for changes to isVisibilityEditing and focus the dropdown when it becomes true
watch(isVisibilityEditing, async (newValue) => {
    if (newValue) {
        // Wait for the DOM to update before focusing
        await nextTick();
        if (visibilityInput.value) {
            visibilityInput.value.$el.focus();
        }
    }
});

// Watch for changes to isUrlEditing and focus the input when it becomes true
watch(isUrlEditing, async (newValue) => {
    if (newValue) {
        // Wait for the DOM to update before focusing
        await nextTick();
        if (urlInput.value) {
            urlInput.value.focus();
        }
    }
});

// Add loading state
const isUrlSaving = ref(false);

// Add loading state for visibility
const isVisibilitySaving = ref(false);

// Add loading state for owner
const isOwnerSaving = ref(false);

// Add loading state for tags
const isTagsSaving = ref(false);

// Add loading state for library
const isLibrarySaving = ref(false);

// Add loading state for team access
const isTeamAccessSaving = ref(false);

// Add loading state for organization access
const isOrgAccessSaving = ref(false);

// Add state for the modal and pending value
const showLibraryChangeModal = ref(false);
const pendingLibraryValue = ref('');

// Function to start editing URL
const startEditingUrl = () => {
    // Close other edit modes
    isVisibilityEditing.value = false;
    isTeamAccessEditing.value = false;
    isLibraryEditing.value = false;
    isOrgAccessEditing.value = false;
    isProductsEditing.value = false;
    
    // Default to empty string if short_name is null/undefined
    editedUrl.value = props.article.short_name || '';
    isUrlEditing.value = true;
};

// Function to save the edited URL
const saveUrlEdit = async () => {
    if (editedUrl.value !== props.article.short_name) {
        isUrlSaving.value = true;
        try {
            await submitArticleFieldUpdate('short_name', editedUrl.value);
            isUrlEditing.value = false;
        } finally {
            isUrlSaving.value = false;
        }
    } else {
        isUrlEditing.value = false;
    }
};

// Function to cancel editing
const cancelEdit = () => {
    isUrlEditing.value = false;
};

// Function to start editing visibility
const startEditingVisibility = () => {
    // Close other edit modes
    isUrlEditing.value = false;
    isTeamAccessEditing.value = false;
    isLibraryEditing.value = false;
    isOrgAccessEditing.value = false;
    isProductsEditing.value = false;
    
    editedVisibility.value = getVisibilityValueFromArticle(props.article);
    isVisibilityEditing.value = true;
};

// Function to save the edited visibility
const saveVisibilityEdit = async () => {
    if (editedVisibility.value !== null && 
        editedVisibility.value !== getVisibilityValueFromArticle(props.article)) {
        isVisibilitySaving.value = true;
        try {
            await submitArticleFieldUpdate('visibility', editedVisibility.value);
            emit('update:field', 'visibility', editedVisibility.value.toString());
            isVisibilityEditing.value = false;
        } finally {
            isVisibilitySaving.value = false;
        }
    } else {
        isVisibilityEditing.value = false;
    }
};

// Function to cancel editing visibility
const cancelVisibilityEdit = () => {
    isVisibilityEditing.value = false;
};

// Handle enter key to save
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
        saveUrlEdit();
    } else if (event.key === 'Escape') {
        cancelEdit();
    }
};

// Define interface for team data from API
interface TeamAccess {
    val: string;
    lbl: string;
    id?: string;
    name?: string;
    description?: string;
}

// Libraries from the store, with loading if needed
const libraries = ref<any[]>([]);
const loadingLibraries = ref(false);

// Teams from the store, with loading if needed
const teams = ref<TeamAccess[]>([]);
const loadingTeams = ref(false);

// Add interface for organization data
interface Organization {
    val: string;
    id: string;
    lbl: string;
    avatar: string;
    url_avatar: string;
    defaultTeamId: string;
    rootKbs: any;
    canRelate: boolean;
    canSponsor: boolean;
    canProvider: boolean;
    canRoute: boolean;
    hasEcosystem: boolean;
    defaultPartner: boolean;
}

// Update the organizations computed property with type
const organizations = computed<Organization[]>(() => {
    return metaStore.partnerMetaData?.pl__all_partners || [];
});

// Define interface for user data from API
interface UserData {
    val: string;
    id: string;
    lbl: string;
    email: string;
    partners_id: string;
}

// Load libraries and teams when the component is mounted
onMounted(async () => {
    document.addEventListener('click', handleClickOutside);
    
    // Check if we already have libraries in the store
    if (knowledgeStore.libraries && knowledgeStore.libraries.length > 0) {
        libraries.value = knowledgeStore.libraries;
    } else {
        // Otherwise load them
        loadingLibraries.value = true;
        try {
            const result = await knowledgeStore.fetchLibrariesOnly();
            libraries.value = result;
        } catch (error) {
            console.error('Error loading libraries:', error);
        } finally {
            loadingLibraries.value = false;
        }
    }
    
    // Check if we already have teams in the store
    if (knowledgeStore.teams && knowledgeStore.teams.length > 0) {
        // Cast the teams to the correct type
        teams.value = knowledgeStore.teams as unknown as TeamAccess[];
    } else {
        // Otherwise load them
        loadingTeams.value = true;
        try {
            const result = await knowledgeStore.fetchTeamsOnly();
            // Cast the result to the correct type
            teams.value = result as unknown as TeamAccess[];
        } catch (error) {
            console.error('Error loading teams:', error);
        } finally {
            loadingTeams.value = false;
        }
    }
    
    // Ensure we have partner metadata loaded
    if (!metaStore.partnerMetaData) {
        await metaStore.loadPartnerMetaData();
    }

    if (partnerStore.products.length === 0) {
        await partnerStore.fetchProducts();
    }

    console.log('Article data:', props.article);
    console.log('Owner ID:', props.article?.c__revision_owner_id);
    console.log('Full article keys:', Object.keys(props.article || {}));

    await knowledgeStore.fetchAvailableKnowledgeBasesOnly();

    if (!knowledgeStore.labelTree.length) {
        await knowledgeStore.fetchLabelTree();
    }
});

// Watch for changes to isLibraryEditing and focus the dropdown when it becomes true
watch(isLibraryEditing, async (newValue) => {
    if (newValue) {
        // Wait for the DOM to update before focusing
        await nextTick();
        if (libraryInput.value) {
            libraryInput.value.$el.focus();
        }
    }
});

// Watch for changes to isTeamAccessEditing and focus the multiselect when it becomes true
watch(isTeamAccessEditing, async (newValue) => {
    if (newValue) {
        // Wait for the DOM to update before focusing
        await nextTick();
        if (teamAccessInput.value) {
            teamAccessInput.value.$el.focus();
        }
    }
});

// Watch for changes to isOrgAccessEditing and focus the multiselect when it becomes true
watch(isOrgAccessEditing, async (newValue) => {
    if (newValue) {
        // Wait for the DOM to update before focusing
        await nextTick();
        if (orgAccessInput.value) {
            orgAccessInput.value.$el.focus();
        }
    }
});

// Watch for changes to isProductsEditing and focus the multiselect when it becomes true
watch(isProductsEditing, async (newValue) => {
    if (newValue) {
        await nextTick();
        if (productsInput.value) {
            productsInput.value.$el.focus();
        }
    }
});

// Function to start editing library
const startEditingLibrary = () => {
    // Close other edit modes
    isUrlEditing.value = false;
    isVisibilityEditing.value = false;
    isTeamAccessEditing.value = false;
    isOrgAccessEditing.value = false;
    isProductsEditing.value = false;
    
    // Set the current library ID
    editedLibrary.value = props.article.root_parent_id || '';
    isLibraryEditing.value = true;
};

// Function to save the edited library
const saveLibraryEdit = async (value?: string) => {
    const newValue = value ?? editedLibrary.value;
    if (newValue !== props.article.root_parent_id) {
        isLibrarySaving.value = true;
        try {
            await knowledgeAPI.updateArticleLibrary(props.article.id, newValue);
            emit('update:field', 'root_parent_id', newValue);
            emit('update', props.article);
            // add 500ms delay
            await new Promise(resolve => setTimeout(resolve, 500));
            isLibraryEditing.value = false;
        } finally {
            isLibrarySaving.value = false;
        }
    } else {
        isLibraryEditing.value = false;
    }
};

// Function to cancel editing library
const cancelLibraryEdit = () => {
    isLibraryEditing.value = false;
};

// Function to start editing team access
const startEditingTeamAccess = () => {
    // Close other edit modes
    isUrlEditing.value = false;
    isVisibilityEditing.value = false;
    isLibraryEditing.value = false;
    isOrgAccessEditing.value = false;
    isProductsEditing.value = false;
    
    // Set the current team access IDs
    editedTeamAccess.value = props.article.internal_team_ids || [];
    isTeamAccessEditing.value = true;
};

// Function to save the edited team access
const saveTeamAccessEdit = async () => {
    // Check if the arrays are different
    const currentTeamIds = props.article.internal_team_ids || [];
    const isChanged = 
        editedTeamAccess.value.length !== currentTeamIds.length ||
        editedTeamAccess.value.some((id: string) => !currentTeamIds.includes(id)) ||
        currentTeamIds.some((id: string) => !editedTeamAccess.value.includes(id));
    
    // Only submit if the values have changed
    if (isChanged) {
        isTeamAccessSaving.value = true;
        try {
            await submitArticleFieldUpdate('internal_team_ids', editedTeamAccess.value);
            emit('update:field', 'internal_team_ids', editedTeamAccess.value);
            isTeamAccessEditing.value = false;
        } finally {
            isTeamAccessSaving.value = false;
        }
    } else {
        isTeamAccessEditing.value = false;
    }
};

// Function to cancel editing team access
const cancelTeamAccessEdit = () => {
    isTeamAccessEditing.value = false;
};

// Function to start editing organization access
const startEditingOrgAccess = () => {
    if (!isOrgAccessEditing.value) {
        // Close any other editing fields first
        closeAllEditingFields();
        
        // Set the initial value based on current partner_ids
        editedOrgAccess.value = props.article.partner_ids?.length 
            ? [...props.article.partner_ids] 
            : [];
            
        // Ensure we have partner metadata loaded
        if (!metaStore.partnerMetaData) {
            metaStore.loadPartnerMetaData();
        }
        
        isOrgAccessEditing.value = true;
    }
};

// Function to save the edited organization access
const saveOrgAccessEdit = async () => {
    if (isOrgAccessEditing.value) {
        isOrgAccessSaving.value = true;
        try {
            await submitArticleFieldUpdate('partner_ids', editedOrgAccess.value);
            emit('update:field', 'partner_ids', editedOrgAccess.value);
            isOrgAccessEditing.value = false;
        } finally {
            isOrgAccessSaving.value = false;
        }
    }
};

// Function to cancel editing organization access
const cancelOrgAccessEdit = () => {
    isOrgAccessEditing.value = false;
};

// Add interface for product data
interface Product {
    id: string;
    dict_id: string;
    c__lbl: string;
}

// Helper to get product name from ID
const getProductNameFromId = (id: string) => {
    const product = partnerStore.products.find(p => p.dict_id === id);
    return product ? product.c__lbl : id;
};

// Function to start editing products
const startEditingProducts = () => {
    // Close other edit modes
    closeAllEditingFields();
    
    // Set the current products - map from dict_id to id for the MultiSelect
    editedProducts.value = (props.article.bc__tags_object_members_devices_dict || []).map((dictId: string) => {
        const product = partnerStore.products.find(p => p.dict_id === dictId);
        return product?.id || dictId;
    });
    isProductsEditing.value = true;
};

// Function to save the edited products
const saveProductsEdit = async () => {
    // Map back from id to dict_id for storage
    const newDictIds = editedProducts.value.map(id => {
        const product = partnerStore.products.find(p => p.id === id);
        return product?.dict_id || id;
    });

    // Check if the arrays are different
    const currentProductIds = props.article.bc__tags_object_members_devices_dict || [];
    const isChanged = 
        newDictIds.length !== currentProductIds.length ||
        newDictIds.some((id: string) => !currentProductIds.includes(id)) ||
        currentProductIds.some((id: string) => !newDictIds.includes(id));
        
    if (isChanged) {
        isProductsSaving.value = true;
        try {
            await submitArticleFieldUpdate('bc__tags_object_members_devices_dict', newDictIds);
            emit('update:field', 'bc__tags_object_members_devices_dict', newDictIds);
            isProductsEditing.value = false;
        } finally {
            isProductsSaving.value = false;
        }
    } else {
        isProductsEditing.value = false;
    }
};

// Function to cancel editing products
const cancelProductsEdit = () => {
    isProductsEditing.value = false;
};

// Modify the click outside handler to include organization access editing
const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    
    if (isUrlEditing.value) {
        // Check if the click is outside the edit field
        const editField = document.querySelector('.url-edit-field');
        if (editField && !editField.contains(target)) {
            saveUrlEdit();
        }
    }
    
    if (isVisibilityEditing.value) {
        // Check if the click is outside the visibility edit field
        const visibilityField = document.querySelector('.visibility-edit-field');
        // Don't close if clicking on the dropdown panel
        const dropdownPanel = document.querySelector('.p-dropdown-panel');
        if (visibilityField && !visibilityField.contains(target) && 
            !(dropdownPanel && dropdownPanel.contains(target))) {
            saveVisibilityEdit();
        }
    }
    
    if (isTeamAccessEditing.value) {
        // Check if the click is outside the team access edit field
        const teamAccessField = document.querySelector('.team-access-edit-field');
        // Don't close if clicking on the dropdown panel
        const dropdownPanel = document.querySelector('.p-multiselect-panel');
        if (teamAccessField && !teamAccessField.contains(target) && 
            !(dropdownPanel && dropdownPanel.contains(target))) {
            saveTeamAccessEdit();
        }
    }
    
    if (isLibraryEditing.value) {
        // Check if the click is outside the library edit field
        const libraryField = document.querySelector('.library-edit-field');
        // Don't close if clicking on the dropdown panel
        const dropdownPanel = document.querySelector('.p-dropdown-panel');
        if (libraryField && !libraryField.contains(target) && 
            !(dropdownPanel && dropdownPanel.contains(target))) {
            saveLibraryEdit();
        }
    }
    
    if (isOrgAccessEditing.value) {
        // Check if the click is outside the organization access edit field
        const orgAccessField = document.querySelector('.org-access-edit-field');
        // Don't close if clicking on the dropdown panel
        const dropdownPanel = document.querySelector('.p-multiselect-panel');
        if (orgAccessField && !orgAccessField.contains(target) && 
            !(dropdownPanel && dropdownPanel.contains(target))) {
            saveOrgAccessEdit();
        }
    }

    if (isProductsEditing.value) {
        // Check if the click is outside the products edit field
        const productsField = document.querySelector('.products-edit-field');
        // Don't close if clicking on the dropdown panel
        const dropdownPanel = document.querySelector('.p-multiselect-panel');
        if (productsField && !productsField.contains(target) && 
            !(dropdownPanel && dropdownPanel.contains(target))) {
            saveProductsEdit();
        }
    }
};

// Add and remove document click event listener
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});

// Helper to get team names from IDs
const getTeamNameFromId = (id: string) => {
    const team = teams.value.find(t => t.val === id || t.id === id);
    return team ? (team.lbl || team.name) : id;
};

// Helper to format team display text
const getTeamDisplayText = computed(() => {
    const teamIds = props.article.internal_team_ids || [];
    
    if (teamIds.length === 0) {
        return 'No team restrictions';
    } else if (teamIds.length === 1) {
        return getTeamNameFromId(teamIds[0]);
    } else {
        return `${teamIds.length} teams selected`;
    }
});

// Computed to check if there are any team restrictions
const hasTeamRestrictions = computed(() => {
    return props.article.internal_team_ids && props.article.internal_team_ids.length > 0;
});

// Helper to get organization names from IDs with proper type annotation
const getOrgNameFromId = (orgId: string | number): string => {
    const org = organizations.value.find((o: Organization) => o.val === orgId || o.id === orgId);
    return org ? org.lbl : `Organization ${orgId}`;
};

// Helper to format organization display text
const getOrgDisplayText = computed(() => {
    const orgIds = props.article.partner_ids || [];
    
    if (orgIds.length === 0) {
        return 'No organization restrictions';
    } else if (orgIds.length === 1) {
        return getOrgNameFromId(orgIds[0]);
    } else {
        return `${orgIds.length} organizations selected`;
    }
});

// Computed for checking if there are partner_ids restrictions
const hasOrgRestrictions = computed(() => {
    return props.article.partner_ids && props.article.partner_ids.length > 0;
});

// Computed property for filtered users based on article's partner_id
const filteredUsers = computed(() => {
    if (!metaStore.partnerMetaData?.pl__partners_users) return [];
    
    console.log(metaStore.partnerMetaData.pl__partners_users);
    return metaStore.partnerMetaData.pl__partners_users.filter((user: UserData) => 
        user.partners_id === props.article.owner_partner_id
    );
});

// Function to start editing owner
const startEditingOwner = () => {
    // Close other edit modes
    closeAllEditingFields();
    
    // Set the current owner
    editedOwner.value = props.article.c__revision_owner_id || '';
    isOwnerEditing.value = true;
};

// Function to save the edited owner
const saveOwnerEdit = async () => {
    if (editedOwner.value !== props.article.c__revision_owner_id) {
        isOwnerSaving.value = true;
        try {
            await submitArticleFieldUpdate('c__revision_owner_id', editedOwner.value);
            emit('update:field', 'c__revision_owner_id', editedOwner.value);
            isOwnerEditing.value = false;
        } finally {
            isOwnerSaving.value = false;
        }
    } else {
        isOwnerEditing.value = false;
    }
};

// Function to cancel editing owner
const cancelOwnerEdit = () => {
    isOwnerEditing.value = false;
};

// Watch for changes to isOwnerEditing and focus the dropdown when it becomes true
watch(isOwnerEditing, async (newValue) => {
    if (newValue) {
        await nextTick();
        if (ownerInput.value) {
            ownerInput.value.$el.focus();
        }
    }
});

// Helper to format products display text
const getProductsDisplayText = computed(() => {
    const productIds = props.article.bc__tags_object_members_devices_dict || [];
    
    if (productIds.length === 0) {
        return 'No products selected';
    } else if (productIds.length === 1) {
        return getProductNameFromId(productIds[0]);
    } else {
        return `${productIds.length} products selected`;
    }
});

// Computed for checking if there are any product restrictions
const hasProductRestrictions = computed(() => {
    return props.article.bc__tags_object_members_devices_dict && props.article.bc__tags_object_members_devices_dict.length > 0;
});

// Helper to get tag name from ID
const getTagNameFromId = (id: string) => {
    const tag = metaStore.metaData?.pl__tags?.find((t: any) => t.id === id || t.val === id);
    return tag ? tag.lbl : id;
};

// Helper to format tags display text
const getTagsDisplayText = computed(() => {
    const tagIds = props.article.bc__tags_support || [];
    
    if (tagIds.length === 0) {
        return 'No tags';
    } else if (tagIds.length === 1) {
        return getTagNameFromId(tagIds[0]);
    } else {
        return `${tagIds.length} tags selected`;
    }
});

// Computed for checking if there are any tag restrictions
const hasTagRestrictions = computed(() => {
    return props.article.bc__tags_support && props.article.bc__tags_support.length > 0;
});

// Function to start editing search terms
const startEditingSearchTerms = () => {
    // Close other edit modes
    closeAllEditingFields();
    
    // Set the current search terms
    editedSearchTerms.value = props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
    isSearchTermsEditing.value = true;
};

// Function to save the edited search terms
const saveSearchTermsEdit = () => {
    // Check if the arrays are different
    const currentTerms = props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
    const isChanged = 
        editedSearchTerms.value.length !== currentTerms.length ||
        editedSearchTerms.value.some((term: string) => !currentTerms.includes(term)) ||
        currentTerms.some((term: string) => !editedSearchTerms.value.includes(term));
        
    // Only emit if the values have changed
    if (isChanged) {
        emit('update:field', 'keywords', editedSearchTerms.value.join(', '));
    }
    isSearchTermsEditing.value = false;
};

// Function to cancel editing search terms
const cancelSearchTermsEdit = () => {
    isSearchTermsEditing.value = false;
};

// Function to handle adding a new term from the MultiSelect
const handleNewSearchTerm = (event: any) => {
    if (event.value && !editedSearchTerms.value.includes(event.value)) {
        editedSearchTerms.value = [...editedSearchTerms.value, event.value];
    }
};

// Watch for changes to isSearchTermsEditing and focus the input when it becomes true
watch(isSearchTermsEditing, async (newValue) => {
    if (newValue) {
        await nextTick();
        if (searchTermsInput.value) {
            searchTermsInput.value.$el.focus();
        }
    }
});

// Modify closeAllEditingFields to include search terms editing
function closeAllEditingFields() {
    isOwnerEditing.value = false;
    isUrlEditing.value = false;
    isVisibilityEditing.value = false;
    isTeamAccessEditing.value = false;
    isLibraryEditing.value = false;
    isOrgAccessEditing.value = false;
    isProductsEditing.value = false;
    isTagsEditing.value = false;
    isSearchTermsEditing.value = false;
    isRelatedArticlesEditing.value = false;
};

// Watch for changes to isTagsEditing and focus the multiselect when it becomes true
watch(isTagsEditing, async (newValue) => {
    if (newValue) {
        await nextTick();
        if (tagsInput.value) {
            tagsInput.value.$el.focus();
        }
    }
});

// Function to start editing tags
const startEditingTags = () => {
    // Close other edit modes
    closeAllEditingFields();
    
    // Set the current tags
    editedTags.value = props.article.bc__tags_support || [];
    isTagsEditing.value = true;
};

// Function to save the edited tags
const saveTagsEdit = async () => {
    // Check if the arrays are different
    const currentTags = props.article.bc__tags_support || [];
    const isChanged = 
        editedTags.value.length !== currentTags.length ||
        editedTags.value.some((id: string) => !currentTags.includes(id)) ||
        currentTags.some((id: string) => !editedTags.value.includes(id));
    
    // Only submit if the values have changed
    if (isChanged) {
        isTagsSaving.value = true;
        try {
            await submitArticleFieldUpdate('bc__tags_support', editedTags.value);
            emit('update:field', 'bc__tags_support', editedTags.value);
            isTagsEditing.value = false;
        } finally {
            isTagsSaving.value = false;
        }
    } else {
        isTagsEditing.value = false;
    }
};

// Function to cancel editing tags
const cancelTagsEdit = () => {
    isTagsEditing.value = false;
};

// Add this computed property
const searchTerms = computed(() => {
    return props.article.keywords ? props.article.keywords.split(',').map((t: string) => t.trim()) : [];
});

// Remove the watch for isReady since we no longer need to fetch organizations
watch(() => props.isReady, async (newValue) => {
    if (newValue && partnerStore.products.length === 0) {
        await partnerStore.fetchProducts();
    }
});

// Update the computed property with logging
const currentOwnerId = computed(() => {
    console.log('Article data for owner lookup:', {
        c__revision_owner_id: props.article.c__revision_owner_id,
        _activeRevisionId: props.article._activeRevisionId,
        owner_id: props.article.owner_id,
        revision_id: props.article.revision_id,
        all_keys: Object.keys(props.article)
    });
    
    // Check for owner ID in different possible locations
    return props.article.c__revision_owner_id || 
           props.article._activeRevisionId || 
           props.article.owner_id;
});

// Function to start editing related articles
const startEditingRelatedArticles = () => {
    closeAllEditingFields();
    editedAutoGenArticles.value = props.article.auto_gen_articles || false;
    editedRelatedArticles.value = props.article.bc__tags_object_kb || [];
    isRelatedArticlesEditing.value = true;
};

// Function to save the edited related articles
const saveRelatedArticlesEdit = async () => {
    isRelatedArticlesSaving.value = true;
    try {
        await submitArticleFieldUpdate('auto_gen_articles', editedAutoGenArticles.value);
        emit('update:field', 'auto_gen_articles', editedAutoGenArticles.value.toString());
        if (!editedAutoGenArticles.value) {
            await submitArticleFieldUpdate('bc__tags_object_kb', editedRelatedArticles.value);
            emit('update:field', 'bc__tags_object_kb', editedRelatedArticles.value);
        }
        emit('update', props.article);
        isRelatedArticlesEditing.value = false;
    } finally {
        isRelatedArticlesSaving.value = false;
    }
};

// Function to cancel editing related articles
const cancelRelatedArticlesEdit = () => {
    isRelatedArticlesEditing.value = false;
};

// Add this computed property
const selectedRelatedArticles = computed(() => {
    if (!props.article?.bc__tags_object_kb) return [];
    return availableKnowledgeBases.value.filter(kb => 
        props.article.bc__tags_object_kb?.includes(kb.id)
    );
});

// For chip display
const selectedLabelNames = computed(() => {
    return (props.article.bc__tags_object_kb_labels || []).map((id: string) => labelMap.value[id] || id);
});

// For edit mode
const isLabelsEditing = ref(false);
const startEditingLabels = () => {
    closeAllEditingFields();
    editedLabels.value = props.article.bc__tags_object_kb_labels ? [...props.article.bc__tags_object_kb_labels] : [];
    isLabelsEditing.value = true;
};
const saveLabelsEdit = () => {
    emit('update:field', 'bc__tags_object_kb_labels', editedLabels.value);
    isLabelsEditing.value = false;
};
const cancelLabelsEdit = () => {
    isLabelsEditing.value = false;
};

// Add this computed property
const labelTreeOptions = computed(() => {
    function mapNode(node: any) {
        return {
            key: node.id,
            label: node.text,
            data: node,
            children: node.children ? node.children.map(mapNode) : undefined,
        };
    }
    return labelTree.value.map(mapNode);
});

// Add a generic submit handler
const submitArticleFieldUpdate = async (field: string, value: any) => {
    try {
        const revisionId = props.article.id;
        const response = await knowledgeStore.updateArticleField(revisionId, field, value);
        if (response && response.success) {
            emit('update', props.article);
            // add 500ms delay
            await new Promise(resolve => setTimeout(resolve, 500));
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Article updated successfully',
                life: 3000,
            });
            
            return true;
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response?.message || 'Failed to update article',
                life: 3000,
            });
            
            return false;
        }
    } catch (error: any) {
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: error?.message || 'Failed to update article',
            life: 3000,
        });
        
        return false;
    }
};

// Add this computed property
const showOrgAccess = computed(() => {
    // Use the numeric visibility field if present, otherwise fallback to mapping
    const vis = typeof props.article.visibility === 'number'
        ? props.article.visibility
        : getVisibilityValueFromArticle(props.article);
    return vis === 2;
    // return vis === 0 || vis === 2;
});

// Add this computed property
const showTeamAccess = computed(() => {
    // Use the numeric visibility field if present, otherwise fallback to mapping
    const vis = typeof props.article.visibility === 'number'
        ? props.article.visibility
        : getVisibilityValueFromArticle(props.article);
    return vis === 1 || vis ===2;
});

// Add the handler for library change
const onLibraryChange = (event: any) => {
    const newValue = event.value;
    if (newValue !== props.article.root_parent_id) {
        pendingLibraryValue.value = newValue;
        showLibraryChangeModal.value = true;
    }
};

// Confirm and cancel handlers for the modal
const confirmLibraryChange = async () => {
    showLibraryChangeModal.value = false;
    await saveLibraryEdit(pendingLibraryValue.value);
    pendingLibraryValue.value = '';
};
const cancelLibraryChange = () => {
    showLibraryChangeModal.value = false;
    pendingLibraryValue.value = '';
    // Optionally reset editedLibrary to the original value
    editedLibrary.value = props.article.root_parent_id;
};

// Add loading state for related articles
const isRelatedArticlesSaving = ref(false);

const isProductsSaving = ref(false);

</script>

<template>
    <BravoAccordionPanel value="0" data-testid="article-info-panel">
        <BravoAccordionHeader data-testid="article-info-header">{{
            t('knowledge.article_information')
        }}</BravoAccordionHeader>
        <BravoAccordionContent data-testid="article-info-content">
            <div v-if="isLoading" class="skeleton-content">
                <Skeleton height="1.5rem" class="mb-2" />
                <Skeleton height="1.5rem" class="mb-2" />
                <Skeleton height="1.5rem" class="mb-2" />
                <Skeleton height="1.5rem" class="mb-2" />
            </div>
            <div v-else class="data-fields" data-testid="article-info-fields">
                <div class="data-field" :class="{ 'horizontal': isWideMode }" data-testid="article-owner-field">
                    <label>Owner</label>
                    <div 
                        class="field-value editable-field owner-edit-field" 
                        @click="startEditingOwner"
                        data-testid="article-owner-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isOwnerEditing" class="display-mode">
                            <i class="pi pi-user" />
                            <span class="clickable-value" @click.stop="startEditingOwner">
                                {{ filteredUsers.find((u: UserData) => u.id === currentOwnerId)?.lbl || 'No owner' }}
                            </span>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <Dropdown 
                                v-model="editedOwner" 
                                :options="filteredUsers"
                                optionLabel="lbl"
                                optionValue="id"
                                ref="ownerInput"
                                class="edit-dropdown"
                                :filter="true"
                                @click.stop
                                data-testid="owner-edit-dropdown"
                            />
                            <div class="edit-actions">
                                <Button
                                    v-if="!isOwnerSaving"
                                    icon="pi pi-check"
                                    class="p-button-text p-button-success"
                                    @click.stop="saveOwnerEdit"
                                    data-testid="save-owner-btn"
                                />
                                <Button
                                    v-if="!isOwnerSaving"
                                    icon="pi pi-times"
                                    class="p-button-text p-button-danger"
                                    @click.stop="cancelOwnerEdit"
                                    data-testid="cancel-owner-edit-btn"
                                />
                                <ProgressSpinner
                                    v-if="isOwnerSaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="owner-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="data-field" :class="{ 'horizontal': isWideMode }" data-testid="article-url-field">
                    <label>URL</label>
                    <div 
                        class="field-value editable-field url-edit-field" 
                        @click="(event) => {
                            // Only prevent editing when clicking directly on the anchor tag
                            const target = event.target as HTMLElement;
                            if (target && target.tagName === 'A') {
                                return;
                            }
                            startEditingUrl();
                        }"
                        data-testid="article-url-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isUrlEditing" class="display-mode">
                            <i class="pi pi-link" />
                            <a
                                :href="article.url"
                                target="_blank"
                                rel="noopener noreferrer"
                                data-testid="article-url-link"
                            >
                                {{
                                    article.url
                                        ? extractPathname(article.url)
                                        : `/article/${article.id}`
                                }}
                            </a>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <input 
                                type="text" 
                                v-model="editedUrl"
                                @keydown="handleKeyDown"
                                @click.stop
                                class="edit-input"
                                ref="urlInput"
                                data-testid="url-edit-input"
                                autocomplete="off"
                            />
                            <div class="edit-actions">
                                <Button
                                    v-if="!isUrlSaving"
                                    icon="pi pi-check"
                                    class="p-button-text p-button-success"
                                    @click.stop="saveUrlEdit"
                                    data-testid="save-url-btn"
                                />
                                <Button
                                    v-if="!isUrlSaving"
                                    icon="pi pi-times"
                                    class="p-button-text p-button-danger"
                                    @click.stop="cancelEdit"
                                    data-testid="cancel-url-edit-btn"
                                />
                                <ProgressSpinner
                                    v-if="isUrlSaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="url-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="data-field" :class="{ 'horizontal': isWideMode }" data-testid="article-visibility-field">
                    <label>Visibility</label>
                    <div 
                        class="field-value editable-field visibility-edit-field" 
                        @click="startEditingVisibility"
                        data-testid="article-visibility-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isVisibilityEditing" class="display-mode">
                            <i
                                :class="[
                                    'pi',
                                    article.c__d_visibility === 'Internal' || article.isPrivate
                                        ? 'pi-lock'
                                        : 'pi-globe',
                                ]"
                            />
                            <span class="clickable-value" @click.stop="startEditingVisibility">
                                {{
                                    article.c__d_visibility ||
                                    (article.isPrivate ? 'Internal' : 'Public')
                                }}
                            </span>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <Dropdown 
                                v-model="editedVisibility" 
                                :options="visibilityOptions"
                                optionLabel="label"
                                optionValue="value"
                                ref="visibilityInput"
                                class="edit-dropdown"
                                @click.stop
                                data-testid="visibility-edit-dropdown"
                            />
                            <div class="edit-actions">
                                <Button 
                                    icon="pi pi-check" 
                                    class="p-button-text p-button-success" 
                                    @click.stop="saveVisibilityEdit" 
                                    data-testid="save-visibility-btn"
                                    :disabled="isVisibilitySaving"
                                    v-if="!isVisibilitySaving"
                                />
                                <Button 
                                    icon="pi pi-times" 
                                    class="p-button-text p-button-danger" 
                                    @click.stop="cancelVisibilityEdit" 
                                    data-testid="cancel-visibility-edit-btn"
                                    :disabled="isVisibilitySaving"
                                    v-if="!isVisibilitySaving"
                                />
                                <ProgressSpinner
                                    v-if="isVisibilitySaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="visibility-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="data-field" :class="{ 'horizontal': isWideMode }" v-if="showTeamAccess" data-testid="article-team-access-field">
                    <label>Team Access</label>
                    <div 
                        class="field-value editable-field team-access-edit-field" 
                        @click="startEditingTeamAccess"
                        data-testid="article-team-access-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isTeamAccessEditing" class="display-mode teams-display-mode" @click.stop="startEditingTeamAccess">
                            <i class="pi pi-users teams-icon" />
                            <div class="teams-content">
                                <!-- Show "No team restrictions" when there are no teams -->
                                <span v-if="!hasTeamRestrictions">No team restrictions</span>
                                <!-- Display teams as chips -->
                                <div v-else class="team-chips">
                                    <BravoTag
                                        v-for="teamId in article.internal_team_ids"
                                        :key="teamId"
                                        :value="getTeamNameFromId(teamId)"
                                        severity="info"
                                        class="team-chip"
                                        @click.stop="startEditingTeamAccess"
                                        :data-testid="`team-chip-${teamId}`"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <MultiSelect 
                                v-model="editedTeamAccess" 
                                :options="teams"
                                optionLabel="lbl"
                                optionValue="val"
                                ref="teamAccessInput"
                                display="chip"
                                class="edit-multiselect"
                                :loading="loadingTeams"
                                @click.stop
                                data-testid="team-access-edit-multiselect"
                            />
                            <div class="edit-actions">
                                <Button
                                    v-if="!isTeamAccessSaving"
                                    icon="pi pi-check"
                                    class="p-button-text p-button-success"
                                    @click.stop="saveTeamAccessEdit"
                                    data-testid="save-team-access-btn"
                                />
                                <Button
                                    v-if="!isTeamAccessSaving"
                                    icon="pi pi-times"
                                    class="p-button-text p-button-danger"
                                    @click.stop="cancelTeamAccessEdit"
                                    data-testid="cancel-team-access-edit-btn"
                                />
                                <ProgressSpinner
                                    v-if="isTeamAccessSaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="team-access-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="data-field" :class="{ 'horizontal': isWideMode }" v-if="showOrgAccess" data-testid="article-org-access-field">
                    <label>Organization Access</label>
                    <div 
                        class="field-value editable-field org-access-edit-field" 
                        @click="startEditingOrgAccess"
                        data-testid="article-org-access-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isOrgAccessEditing" class="display-mode orgs-display-mode" @click.stop="startEditingOrgAccess">
                            <i class="pi pi-building orgs-icon" />
                            <div class="orgs-content">
                                <!-- Show "No organization restrictions" when there are no orgs -->
                                <span v-if="!hasOrgRestrictions">No organization restrictions</span>
                                <!-- Display orgs as chips -->
                                <div v-else class="org-chips">
                                    <BravoTag
                                        v-for="orgId in article.partner_ids"
                                        :key="orgId"
                                        :value="getOrgNameFromId(orgId)"
                                        severity="info"
                                        class="org-chip"
                                        @click.stop="startEditingOrgAccess"
                                        :data-testid="`org-chip-${orgId}`"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <MultiSelect 
                                v-model="editedOrgAccess" 
                                :options="organizations"
                                optionLabel="lbl"
                                optionValue="val"
                                :filter="true"
                                :showClear="true"
                                placeholder="Select Organizations"
                                display="chip"
                                class="edit-multiselect"
                                :loading="metaStore.isLoading"
                                @click.stop
                                data-testid="org-access-edit-multiselect"
                            >
                                <template #option="slotProps">
                                    <div class="org-option">
                                        <img 
                                            :src="slotProps.option.avatar" 
                                            :alt="slotProps.option.lbl"
                                            class="org-avatar"
                                        />
                                        <span>{{ slotProps.option.lbl }}</span>
                                    </div>
                                </template>
                                <template #value="slotProps">
                                    <div class="org-value" v-if="slotProps.value">
                                        <BravoTag
                                            v-for="orgId in slotProps.value"
                                            :key="orgId"
                                            :value="getOrgNameFromId(orgId)"
                                            severity="info"
                                            class="org-chip"
                                        />
                                    </div>
                                    <span v-else>Select Organizations</span>
                                </template>
                            </MultiSelect>
                            <div class="edit-actions">
                                <Button
                                    v-if="!isOrgAccessSaving"
                                    icon="pi pi-check"
                                    class="p-button-text p-button-success"
                                    @click.stop="saveOrgAccessEdit"
                                    data-testid="save-org-access-btn"
                                />
                                <Button
                                    v-if="!isOrgAccessSaving"
                                    icon="pi pi-times"
                                    class="p-button-text p-button-danger"
                                    @click.stop="cancelOrgAccessEdit"
                                    data-testid="cancel-org-access-edit-btn"
                                />
                                <ProgressSpinner
                                    v-if="isOrgAccessSaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="org-access-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div 
                    class="data-field"
                    :class="{ 'horizontal': isWideMode }"
                    data-testid="article-library-field"
                >
                    <label>Library</label>
                    <div 
                        class="field-value editable-field library-edit-field" 
                        @click="startEditingLibrary"
                        data-testid="article-library-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isLibraryEditing" class="display-mode">
                            <i class="pi pi-book" />
                            <span class="clickable-value" @click.stop="startEditingLibrary">
                                {{ article.library_name || 'Unknown Library' }}
                            </span>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <Dropdown 
                                v-model="editedLibrary" 
                                :options="libraries"
                                optionLabel="name"
                                optionValue="id"
                                ref="libraryInput"
                                class="edit-dropdown"
                                :loading="loadingLibraries"
                                @change="onLibraryChange"
                                @click.stop
                                data-testid="library-edit-dropdown"
                            />
                            <div class="edit-actions">
                                <Button
                                    v-if="!isLibrarySaving"
                                    icon="pi pi-check"
                                    class="p-button-text p-button-success"
                                    @click.stop="() => saveLibraryEdit()"
                                    data-testid="save-library-btn"
                                />
                                <Button
                                    v-if="!isLibrarySaving"
                                    icon="pi pi-times"
                                    class="p-button-text p-button-danger"
                                    @click.stop="cancelLibraryEdit"
                                    data-testid="cancel-library-edit-btn"
                                />
                                <ProgressSpinner
                                    v-if="isLibrarySaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="library-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <TagFormInput
                    v-if="article.bc__tags_support"
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-tags-input"
                />
                <ProductFormInput
                    v-if="article.bc__tags_object_members_devices_dict"
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                    data-testid="article-products-input"
                />
                <SearchTermsFormInput
                    :article="article"
                    :isLoading="isLoading"
                    :isSaving="false"
                    :isHorizontal="isWideMode"
                    :onSubmit="submitArticleFieldUpdate"
                    @update="() => emit('update', article)"
                />
                <div
                    class="data-field"
                    :class="{ 'horizontal': isWideMode }"
                    v-if="article.bc__tags_object_kb_labels?.length"
                    data-testid="article-labels-field"
                >
                    <label>Labels</label>
                    <div class="field-value editable-field labels-edit-field" @click="startEditingLabels" data-testid="article-labels-value">
                        <div v-if="!isLabelsEditing" class="display-mode labels-display-mode" @click.stop="startEditingLabels">
                            <i class="pi pi-tags tags-icon" />
                            <div class="labels-content">
                                <span v-if="selectedLabelNames.length === 0">No labels</span>
                                <div v-else class="label-chips">
                        <BravoTag
                                        v-for="label in selectedLabelNames"
                            :key="label"
                            :value="label"
                            severity="info"
                                        class="label-chip"
                                        @click.stop="startEditingLabels"
                                    />
                                </div>
                            </div>
                        </div>
                        <div v-else class="edit-container">
                            <TreeSelect
                                v-model="editedLabels"
                                :options="labelTreeOptions"
                                selectionMode="multiple"
                                display="chip"
                                placeholder="Select labels"
                                class="w-full"
                                :filter="true"
                                :disabled="!isReady"
                                data-testid="labels-edit-treeselect"
                                optionLabel="label"
                                optionValue="key"
                                :metaKeySelection="false"
                            />
                            <div class="edit-actions">
                                <Button icon="pi pi-check" class="p-button-text" @click="saveLabelsEdit" />
                                <Button icon="pi pi-times" class="p-button-text p-button-danger" @click="cancelLabelsEdit" />
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="data-field"
                    :class="{ 'horizontal': isWideMode }"
                    data-testid="article-related-articles-field"
                >
                    <label>Related Articles</label>
                    <div 
                        class="field-value editable-field related-articles-edit-field" 
                        @click="startEditingRelatedArticles"
                        data-testid="article-related-articles-value"
                    >
                        <!-- Display mode -->
                        <div v-if="!isRelatedArticlesEditing" class="display-mode related-articles-display-mode">
                            <i class="pi pi-link" />
                            <div class="related-articles-content">
                                <span v-if="article.auto_gen_articles">Auto Generated</span>
                                <span v-else>Manual Selection</span>
                            </div>
                        </div>
                        
                        <!-- Edit mode -->
                        <div v-else class="edit-container">
                            <div>
                                <div class="auto-gen-toggle">
                                    <ToggleSwitch
                                        v-model="editedAutoGenArticles"
                                        @click.stop
                                        data-testid="auto-gen-articles-toggle"
                                    />
                                    <label class="toggle-label">Auto Generate</label>
                                </div>
                                <template v-if="!editedAutoGenArticles">
                                    <div class="related-articles-select">
                                        <MultiSelect
                                            v-model="editedRelatedArticles"
                                            :options="availableKnowledgeBases"
                                            optionLabel="lbl"
                                            optionValue="id"
                                            :loading="loadingKnowledgeBases"
                                            placeholder="Select related articles"
                                            display="chip"
                                            class="w-full"
                                            :disabled="!isReady"
                                        />
                                    </div>
                                </template>
                            </div>
                            <div class="edit-actions">
                                <Button
                                    v-if="!isRelatedArticlesSaving"
                                    icon="pi pi-check"
                                    class="p-button-text"
                                    @mousedown.stop
                                    @click.stop="saveRelatedArticlesEdit"
                                />
                                <Button
                                    v-if="!isRelatedArticlesSaving"
                                    icon="pi pi-times"
                                    class="p-button-text p-button-danger"
                                    @mousedown.stop
                                    @click="cancelRelatedArticlesEdit"
                                />
                                <ProgressSpinner
                                    v-if="isRelatedArticlesSaving"
                                    style="width: 1.5rem; height: 1.5rem"
                                    strokeWidth="4"
                                    data-testid="related-articles-saving-spinner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </BravoAccordionContent>
    </BravoAccordionPanel>
    <BravoDialog v-model:visible="showLibraryChangeModal" modal :closable="true" :closeOnEscape="true" :dismissableMask="true" style="max-width: 420px;">
        <template #header>
            <span style="display: flex; align-items: center; gap: 1rem;">
                <i class="pi pi-exclamation-triangle" style="color: #f59e42; font-size: 2rem;" />
                <span style="font-weight: bold; font-size: 1.25rem;">Change Article Library</span>
            </span>
        </template>
        <div>
            <p>
                Changing the library will move this article, including all the drafts/revisions, to a different Knowledge Base Library.
            </p>
            <p style="font-weight: bold;">
                Moving this article will also remove all labels from every draft/revision on this article.
            </p>
            <p>Are you sure you want to proceed?</p>
        </div>
        <template #footer>
            <Button label="No" class="p-button-text" @click="cancelLibraryChange" />
            <Button label="Yes" class="p-button-warning" @click="confirmLibraryChange" autofocus />
        </template>
    </BravoDialog>
</template>

<style scoped>
.data-fields {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.data-field {
    display: flex;
    flex-direction: column;
}

/* This class will be applied when sidebar is wider */
.data-field.horizontal {
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0.5rem;
}

.data-field.horizontal label {
    flex: 0 0 25%;
    margin-bottom: 0;
    padding-top: 0.25rem;
}

.data-field.horizontal .field-value {
    flex: 0 0 70%;
}

.data-field label {
    font-size: 0.75rem;
    color: #64748b;
    display: block;
    margin-bottom: 0.25rem;
}

.data-field .field-value {
    font-size: 0.813rem;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.data-field .field-value i {
    color: #64748b;
    font-size: 1rem;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background-color: var(--surface-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.search-terms {
    color: #64748b;
    font-size: 0.813rem;
    line-height: 1.4;
}

.skeleton-content {
    padding: 0.5rem 0;
}

.skeleton-content .p-skeleton {
    margin-bottom: 0.75rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

/* Editable field styles */
.field-value.editable-field {
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
    margin: -4px -8px;
    padding: 4px 8px;
    border-radius: 4px;
    width: 100%;
}

.field-value.editable-field:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.field-value.editable-field a {
    color: var(--primary-color, #3B82F6);
    text-decoration: none;
}

.field-value.editable-field a:hover {
    text-decoration: underline;
    cursor: pointer;
}

.edit-container {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 8px;
    justify-content: space-between;
}

.edit-input {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid var(--surface-300, #ddd);
    border-radius: 4px;
    font-size: 0.813rem;
    width: 100%;
}

.edit-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.edit-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background: transparent;
    transition: background-color 0.2s;
}

.edit-action-btn.save {
    color: var(--green-600, #22c55e);
}

.edit-action-btn.cancel {
    color: var(--red-600, #ef4444);
}

.edit-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.display-mode {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.edit-dropdown {
    flex: 1;
    width: 100%;
    font-size: 0.813rem;
}

.edit-dropdown :deep(.p-dropdown) {
    width: 100%;
}

.edit-multiselect {
    flex: 1;
    width: 100%;
    font-size: 0.813rem;
}

.edit-multiselect :deep(.p-multiselect) {
    width: 100%;
}

/* Team chips styling */
.team-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.team-chip {
    cursor: pointer;
    transition: transform 0.1s ease;
}

.team-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Teams display mode */
.teams-display-mode {
    display: flex;
    align-items: flex-start;
    width: 100%;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
    gap: 0.5rem;
}

.teams-display-mode:hover {
    background-color: transparent;
}

.team-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
}

.teams-icon {
    color: var(--primary-color, #3B82F6);
    font-size: 1rem;
    margin-top: 3px;
}

.teams-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* Clickable value styles */
.clickable-value {
    cursor: pointer;
}

/* Org chips styling */
.org-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.org-chip {
    cursor: pointer;
    transition: transform 0.1s ease;
}

.org-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Orgs display mode */
.orgs-display-mode {
    display: flex;
    align-items: flex-start;
    width: 100%;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
    gap: 0.5rem;
}

.orgs-display-mode:hover {
    background-color: transparent;
}

.orgs-icon {
    color: var(--primary-color, #3B82F6);
    font-size: 1rem;
    margin-top: 3px;
}

.orgs-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* Products display mode */
.products-display-mode {
    display: flex;
    align-items: flex-start;
    width: 100%;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
    gap: 0.5rem;
}

.products-display-mode:hover {
    background-color: transparent;
}

.products-icon {
    color: var(--primary-color, #3B82F6);
    font-size: 1rem;
    margin-top: 3px;
}

.products-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.product-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.product-chip {
    cursor: pointer;
    transition: transform 0.1s ease;
}

.product-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Tags display mode */
.tags-display-mode {
    display: flex;
    align-items: flex-start;
    width: 100%;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
    gap: 0.5rem;
}

.tags-display-mode:hover {
    background-color: transparent;
}

.tags-icon {
    color: var(--primary-color, #3B82F6);
    font-size: 1rem;
    margin-top: 3px;
}

.tags-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.tag-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.tag-chip {
    cursor: pointer;
    transition: transform 0.1s ease;
}

.tag-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-terms-display-mode {
    display: flex;
    align-items: flex-start;
    width: 100%;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
    gap: 0.5rem;
}

.search-terms-display-mode:hover {
    background-color: transparent;
}

.search-terms-icon {
    color: var(--primary-color, #3B82F6);
    font-size: 1rem;
    margin-top: 3px;
}

.search-terms-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.search-terms-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.search-term-chip {
    cursor: pointer;
    transition: transform 0.1s ease;
}

.search-term-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-terms-edit-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.new-search-term-input {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.new-search-term-input input {
    flex: 1;
}

.edit-action-btn.add {
    color: var(--primary-color, #3B82F6);
}

.edit-action-btn.add:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.p-multiselect-empty-message {
    padding: 0.5rem;
    color: var(--text-color-secondary);
    font-style: italic;
}

.org-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
}

.org-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.org-value {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.related-articles-display-mode {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
    gap: 0.75rem;
    min-height: 32px;
}

.related-articles-content {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-color-secondary);
    font-weight: 500;
}

.auto-gen-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toggle-label {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
}

.chips-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.article-chip {
    background-color: var(--surface-200);
    color: var(--text-color);
}

.related-articles-select {
    margin: 1rem 0;
    width: 100%;
}

.empty-value {
    color: var(--text-color-secondary);
    font-style: italic;
}

.labels-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.label-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.label-chip {
    cursor: pointer;
    transition: transform 0.1s ease;
}

.label-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style> 