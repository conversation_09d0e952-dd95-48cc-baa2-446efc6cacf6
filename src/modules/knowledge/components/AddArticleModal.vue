<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { useToast } from 'primevue/usetoast';
import { useKnowledgeAPI } from '../../../composables/services/useKnowledgeAPI';
import { useRouter } from 'vue-router';

import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import BravoSelect from '@services/ui-component-library/components/BravoSelectField.vue';
import type { KnowledgeLibrary } from '../../services/KnowledgeAPI';

const store = useKnowledgeStore();
const toast = useToast();
const router = useRouter();
const knowledgeAPI = useKnowledgeAPI();

const visible = ref(false);
const title = ref('');
const selectedLibraryId = ref('');
const loading = ref(false);

// Format libraries for the dropdown
const libraryOptions = ref<Array<{ label: string; value: string }>>([]);

onMounted(() => {
    // No action needed on mount
});

const provideFallbackOptions = () => {
    // Use a default/fallback library option if API fails
    libraryOptions.value = [{ label: 'Default Library', value: 'default' }];
    selectedLibraryId.value = 'default';
};

const showModal = async () => {
    // Reset form
    title.value = '';
    visible.value = true;

    // Only try to load libraries if we don't already have options
    if (libraryOptions.value.length === 0) {
        try {
            console.log('Fetching libraries for dropdown...');
            // Directly fetch libraries from API without touching the store
            const libraries = await knowledgeAPI.fetchKnowledgeLibraries();

            if (libraries && libraries.length > 0) {
                console.log('Libraries fetched successfully:', libraries);
                // Format libraries for dropdown without updating the store
                libraryOptions.value = libraries.map((lib: KnowledgeLibrary) => ({
                    label: lib.name,
                    value: lib.id,
                }));

                console.log('Library options prepared:', libraryOptions.value);

                // Select the first one
                if (libraryOptions.value.length > 0) {
                    selectedLibraryId.value = libraryOptions.value[0].value;
                    console.log('Selected library:', selectedLibraryId.value);
                }
            } else {
                console.warn('No libraries found, using fallback');
                // Use fallback if no libraries found
                provideFallbackOptions();
            }
        } catch (error) {
            console.error('Error loading libraries:', error);
            provideFallbackOptions();
        }
    }
};

const hideModal = () => {
    visible.value = false;
};

const createArticle = async () => {
    if (!title.value.trim()) {
        toast.add({
            severity: 'warn',
            summary: 'Validation Error',
            detail: 'Please enter a title for the article',
            life: 3000,
        });
        return;
    }

    if (!selectedLibraryId.value) {
        toast.add({
            severity: 'warn',
            summary: 'Validation Error',
            detail: 'Please select a library',
            life: 3000,
        });
        return;
    }

    loading.value = true;

    try {
        console.log('Creating article with title:', title.value, 'and library ID:', selectedLibraryId.value);

        // Create article payload
        const articleData = {
            title: title.value,
            root_parent_id: selectedLibraryId.value,
            short_name: title.value.toLowerCase().replace(/\s+/g, '-'),
            id: 'boomtown.kb.model.Article-1', // This will be replaced by server
            owner_partner_id: '',
            partner_ids: [],
            internal_team_ids: [],
            sub_title: '',
            body: '',
            saved_reply: '',
            visibility: 1,
            searchable: true,
            status: 0, // Published
            type: 0,
            updated: '',
            created: '',
            _merge: '',
            merge_ids: '',
            url: '',
            url_avatar: '',
            bc__tags_object_kb: [],
            bc__tags_positions_object_kb: [],
            c__contributor_user_ids: [],
            _latestDraftId: '',
            auto_gen_articles: false,
            bc__tags_object_kb_labels: [],
            c__org_names: [],
            bc__tags_object_members_devices_dict: [],
            c__tech_labels: [],
            bc__tags_support: [],
            c__support_labels: [],
            editor_user_ids: [],
            c__editor_user_names: [],
            keywords: '',
            url_login: '',
            _canWrite: true,
            _uiAccess: {
                edit: true,
                merge: true,
                clone: true,
                delete: true,
            },
        };

        // Call API directly instead of going through the store
        const response = await knowledgeAPI.createKnowledgeArticle(articleData);
        console.log('Article creation response (full):', JSON.stringify(response, null, 2));

        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Article created successfully',
            life: 3000,
        });

        hideModal();

        // Extract the article ID from the response
        let articleId: string | null = null;

        // Recursive function to search for IDs in nested objects
        const findIdInObject = (obj: any, path = ''): string | null => {
            if (!obj || typeof obj !== 'object') return null;

            // If the object has an id property, use it
            if (obj.id && typeof obj.id === 'string') {
                console.log(`Found ID at ${path}.id:`, obj.id);
                return obj.id;
            }

            // Search in nested objects and arrays
            for (const key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    const value = obj[key];

                    // Skip functions and null values
                    if (typeof value === 'function' || value === null) continue;

                    // If the value is a string and looks like an ID, use it
                    if (typeof value === 'string' && key.toLowerCase().includes('id') && value.length > 8) {
                        console.log(`Found potential ID at ${path}.${key}:`, value);
                        return value;
                    }

                    // Recursively search in nested objects
                    if (typeof value === 'object') {
                        const foundId = findIdInObject(value, `${path}.${key}`);
                        if (foundId) return foundId;
                    }
                }
            }

            return null;
        };

        // Try to find the article ID in different possible locations in the response
        if (response) {
            console.log('Response type:', typeof response);
            if (typeof response === 'object') {
                console.log('Response keys:', Object.keys(response));
            }

            // Check for kb_ids array which is the most common response format
            if (response.kb_ids && Array.isArray(response.kb_ids) && response.kb_ids.length > 0) {
                console.log('Found ID in kb_ids array:', response.kb_ids[0]);
                articleId = response.kb_ids[0];
            } else if (response.id) {
                console.log('Found ID directly on response object:', response.id);
                articleId = response.id;
            } else if (response.data?.id) {
                console.log('Found ID in response.data:', response.data.id);
                articleId = response.data.id;
            } else if (Array.isArray(response.data) && response.data.length > 0) {
                console.log('Found data array, first item:', response.data[0]);
                if (response.data[0].id) {
                    console.log('Found ID in first data array item:', response.data[0].id);
                    articleId = response.data[0].id;
                }
            } else if (response.kb?.id) {
                console.log('Found ID in response.kb:', response.kb.id);
                articleId = response.kb.id;
            } else if (Array.isArray(response.kb) && response.kb.length > 0) {
                console.log('Found kb array, first item:', response.kb[0]);
                if (response.kb[0].id) {
                    console.log('Found ID in first kb array item:', response.kb[0].id);
                    articleId = response.kb[0].id;
                }
            } else if (typeof response === 'string') {
                console.log('Response is directly a string:', response);
                articleId = response;
            } else if (response.success && response.message && response.message.includes('id=')) {
                // Try to extract the ID from a success message like "Article saved with id=12345"
                const idMatch = response.message.match(/id=([^&\s]+)/);
                if (idMatch && idMatch[1]) {
                    console.log('Extracted ID from message:', idMatch[1]);
                    articleId = idMatch[1];
                }
            }

            // If we still don't have an ID, do a deep search
            if (!articleId) {
                articleId = findIdInObject(response, 'response');
            }
        }

        console.log('Final extracted article ID:', articleId);

        // If we have an article ID, navigate to it in edit mode
        if (articleId) {
            console.log(`Navigating to article ${articleId} in edit mode`);

            // Add small delay to ensure navigation happens after modal closing
            setTimeout(() => {
                // Use replace instead of push to avoid navigation issues
                // and ensure a clean navigation without adding to history stack
                router.replace({
                    path: `/knowledge/articles/${articleId}`,
                    query: { mode: 'edit' },
                });
                console.log('Navigation triggered');

                // If navigation doesn't work, try again with a different approach
                setTimeout(() => {
                    if (window.location.pathname !== `/knowledge/articles/${articleId}`) {
                        console.log('Navigation may have failed, trying direct URL change');
                        window.location.href = `/knowledge/articles/${articleId}?mode=edit`;
                    }
                }, 300);
            }, 100);
        } else {
            console.warn('Could not navigate to article - ID not found in response:', response);

            // Still refresh the list to show the new article even if we can't navigate to it
            if (store.currentRootId) {
                // Refresh the list to show the new article (only if in a specific library view)
                store.fetchKnowledgeListing({
                    rootParentId: store.currentRootId,
                    parentId: null,
                });
            }
        }
    } catch (error) {
        console.error('Error creating article:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: error instanceof Error ? error.message : 'Failed to create article',
            life: 5000,
        });
    } finally {
        loading.value = false;
    }
};

defineExpose({
    showModal,
});
</script>

<template>
    <BravoDialog
        v-model:visible="visible"
        header="Create Article"
        :modal="true"
        :closable="true"
        :style="{ width: '450px' }"
        :draggable="false"
    >
        <div class="add-article-form">
            <div class="form-field">
                <label for="article-title">Title</label>
                <BravoInputText
                    id="article-title"
                    v-model="title"
                    placeholder="Enter article title"
                    :autoFocus="true"
                    class="w-full"
                />
            </div>

            <div class="form-field">
                <label for="article-library">Library</label>
                <BravoSelect
                    id="article-library"
                    v-model="selectedLibraryId"
                    :options="libraryOptions"
                    placeholder="Select a library"
                    class="w-full"
                    dataTestId="article-library-select"
                />
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <BravoButton label="Cancel" severity="secondary" @click="hideModal" :disabled="loading" />
                <BravoButton label="Create" @click="createArticle" :loading="loading" />
            </div>
        </template>
    </BravoDialog>
</template>

<style scoped>
.add-article-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 0.5rem 0;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-field label {
    font-weight: 500;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}
</style>
../../features/kb/stores/knowledge
