<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import ArticleFormInput from './ArticleFormInput.vue';
import Button from 'primevue/button';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Compute the display values for the search terms
const searchTermsDisplayValues = computed(() => {
  return props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
});

// Track the edited value locally
const editedSearchTerms = ref<string[]>([]);

// Track the current filter text
const filterText = ref('');

// Initialize the edited search terms when the article changes
watch(() => props.article.keywords, (newKeywords) => {
  editedSearchTerms.value = newKeywords ? newKeywords.split(',').map((term: string) => term.trim()) : [];
}, { immediate: true });

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the ArticleFormInput component
const formInputRef = ref<InstanceType<typeof ArticleFormInput> | null>(null);

// Handle the update event from ArticleFormInput
const handleUpdate = (fieldName: string, value: any) => {
  editedSearchTerms.value = value;
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Add a new term if not present and not empty
const addNewTerm = async () => {
  const term = filterText.value.trim();
  if (term && !editedSearchTerms.value.includes(term)) {
    editedSearchTerms.value = [...editedSearchTerms.value, term];
    await nextTick();
    filterText.value = '';
    formInputRef.value?.clearFilter();
    // Optionally close the dropdown:
    // formInputRef.value?.closeDropdown();
  }
};

// Handle the save event from ArticleFormInput
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Check if the arrays are different
    const currentTerms = props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
    const isChanged = 
      value.length !== currentTerms.length ||
      value.some((term: string) => !currentTerms.includes(term)) ||
      currentTerms.some((term: string) => !value.includes(term));
      
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit('keywords', value.join(', '));
      formInputRef.value?.handleSaveComplete(success);
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving search terms:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedSearchTerms.value = props.article.keywords ? props.article.keywords.split(',').map((term: string) => term.trim()) : [];
  filterText.value = '';
};

// Function to handle adding a new term
const handleNewSearchTerm = () => {
  addNewTerm();
};

// Handle filter event from MultiSelect
const handleFilter = (event: any) => {
  filterText.value = event.value;
};

// Handle keydown.enter event from MultiSelect
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && filterText.value.trim() && canAddTerm) {
    addNewTerm();
    event.preventDefault();
  }
};

// Computed property to determine if we can add the current filter text
const canAddTerm = computed(() => {
  const term = filterText.value.trim();
  return term && !editedSearchTerms.value.includes(term);
});
</script>

<template>
  <ArticleFormInput
    ref="formInputRef"
    label="Search Terms"
    fieldName="searchTerms"
    :value="editedSearchTerms"
    :displayValue="searchTermsDisplayValues"
    inputType="multiselect"
    displayType="chips"
    :options="editedSearchTerms"
    :isLoading="isLoading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    iconClass="pi pi-search"
    noValueText="No search terms"
    dataTestId="article-search-terms"
    showFilter
    filterPlaceholder="Add New Term"
    enforceSubmitButton
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
    @filter="handleFilter"
    @keydown.enter="handleKeydown"
  >
    <template #emptyfilter>
      <span></span>
      </template>
      <template #footer>
        <div class="add-term-btn-container p-4" >
          <Button
            v-if="canAddTerm"
            class="add-term-btn"
            @mousedown.prevent="handleNewSearchTerm"
            :label="`Add '${filterText.trim()}'`"
            icon="pi pi-plus"
            size="small"
            outlined
          />
        </div>
    </template>
  </ArticleFormInput>
</template> 



<style scoped>

.add-term-btn-container {
 display: flex;
 justify-content: flex-start;
 align-items: center;
 gap: 10px;
 padding: 10px;
}

</style>