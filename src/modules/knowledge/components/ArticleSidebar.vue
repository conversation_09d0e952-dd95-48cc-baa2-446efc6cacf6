<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import BravoBlock from '@services/ui-component-library/components/BravoBlock.vue';
import BravoAccordionPanel from '@services/ui-component-library/components/BravoAccordionPanel.vue';
import BravoAccordionHeader from '@services/ui-component-library/components/BravoAccordionHeader.vue';
import BravoAccordionContent from '@services/ui-component-library/components/BravoAccordionContent.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import BravoComment from '@services/ui-component-library/components/BravoComment.vue';
import Tabs from 'primevue/tabs';
import TabList from 'primevue/tablist';
import Tab from 'primevue/tab';
import TabPanels from 'primevue/tabpanels';
import TabPanel from 'primevue/tabpanel';
import Timeline from 'primevue/timeline';
import BravoTimeline from '@services/ui-component-library/components/BravoTimeline.vue';
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue';
import FilterBtn from '@/modules/knowledge/components/FilterButton.vue';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '../../../stores/user';
import { stripHtmlAndDecodeEntities } from '../utils/helpers';
import { formatDate, formatRelativeTime, extractPathname } from '../utils/formatHelpers';
import type { RelatedArticle, KnowledgeEvent } from '../../../services/KnowledgeAPI';
import { knowledgeAPI } from '../../../services/KnowledgeAPI';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import Skeleton from 'primevue/skeleton';
import ArticleInformationBlock from './ArticleInformationBlock.vue';
import { useKnowledgeStore } from '../stores/knowledge';
import { storeToRefs } from 'pinia';
import { useToast } from 'primevue/usetoast';
import BravoTextarea from '@services/ui-component-library/components/BravoTextarea.vue';
import { useKnowledgeAPI as useKnowledgeApiInstance } from '@/composables/services/useKnowledgeAPI';

const { t } = useI18n();
const userStore = useUserStore();
const knowledgeStore = useKnowledgeStore();
const { comments: articleComments, loadingComments: loadingArticleComments } = storeToRefs(knowledgeStore);
const toast = useToast();
const resolvingCommentIds = ref<string[]>([]);
const showResolved = ref(false);
const addingComment = ref(false);
const newComment = ref('');
const submittingComment = ref(false);

// Define props
const props = defineProps<{
    article: any;
    relatedArticles: RelatedArticle[];
    loadingRelatedArticles: boolean;
    isPanelMinimized: boolean;
    isLoading?: boolean;
}>();

// Define emits
const emit = defineEmits<{
    (e: 'toggle-panel'): void;
    (e: 'update:article', field: string, value: string): void;
    (e: 'update'): void;
}>();

// State for events
const events = ref<KnowledgeEvent[]>([]);
const loadingEvents = ref(false);

const showFilter = ref(false);
const totalEventCount = ref(0);
const currEventPage = ref(0);
const currentFilterIndexes = ref<number[]>([]);

/**
 * Handle filter change from the filter button
 * This stores the selected filters but doesn't apply them immediately
 * The filters will be applied when the popover is hidden
 * @param filterIndexes Array of event type IDs to filter by
 */
const onFilterChange = async (filterIndexes: number[]) => {
  // Store the current filter indexes for use when the popover is hidden
  currentFilterIndexes.value = filterIndexes;
};

/**
 * Refresh events list by clearing current events and fetching the first page
 */
const refreshEvents = async () => {
  if (!props.article?.kb_id) {
    console.warn('Cannot refresh events: article KB ID is missing');
    return;
  }

  // Reset pagination and clear events
  currEventPage.value = 0;
  events.value = [];

  // Fetch events with current filters
  await fetchEventsWithFilters(currentFilterIndexes.value);
};

/**
 * Fetch article events with specified filters
 * @param filterIndexes Array of event type IDs to filter by
 */
const fetchEventsWithFilters = async (filterIndexes: number[] = []) => {
  if (!props.article?.kb_id) {
    console.warn('Cannot fetch events: article KB ID is missing');
    return;
  }

  loadingEvents.value = true;
  events.value = []; // Clear existing events when applying filters
  currEventPage.value = 0; // Reset pagination

  // Base filter parameters
  const filter: Array<{property: string, operator?: string, value: any}> = [
    { property: 'kb_id', value: props.article.kb_id },
    { property: 'root_kb_id', value: '_no_filter_' },
    { property: 'limit_diff_log', value: true },
  ];

  // Add type filter based on selected filter indexes
  if (filterIndexes.length > 0) {
    filter.push({
      property: 'type',
      operator: 'in',
      value: filterIndexes,
    });
  } else {
    // If no filters selected, show all event types
    filter.push({
      property: 'type',
      operator: 'ne',
      value: '_no_filter_'
    });
  }

  try {
    console.log('Fetching events with filter:', JSON.stringify(filter));
    const result = await useKnowledgeAPI().fetchArticleEvents(props.article.kb_id, {
      page: 1,
      start: 0,
      limit: 15,
      filter: filter,
    });

    events.value = result.items;
    totalEventCount.value = result.totalCount;
  } catch (error) {
    console.error('Error fetching article events:', error);
    events.value = [];
    toast.add({
      severity: 'error',
      summary: t('knowledge.error'),
      detail: t('knowledge.failed_to_fetch_events') || 'Failed to fetch events',
      life: 3000
    });
  } finally {
    loadingEvents.value = false;
  }
};

/**
 * Fetch article events with pagination
 * Loads the next page of events and appends to the existing list
 */
const fetchEvents = async () => {
  if (!props.article?.kb_id) {
    console.warn('Cannot fetch events: article KB ID is missing');
    return;
  }

  loadingEvents.value = true;

  const limit = 15;
  const start = currEventPage.value * limit;

  // Base filter parameters
  const filter: Array<{property: string, operator?: string, value: any}> = [
    { property: 'kb_id', value: props.article.kb_id },
    { property: 'root_kb_id', value: '_no_filter_' },
    { property: 'limit_diff_log', value: true },
  ];

  // Add type filter based on current filter indexes
  if (currentFilterIndexes.value.length > 0) {
    filter.push({
      property: 'type',
      operator: 'in',
      value: currentFilterIndexes.value,
    });
  } else {
    // If no filters selected, show all event types
    filter.push({
      property: 'type',
      operator: 'ne',
      value: '_no_filter_'
    });
  }

  try {
    console.log('Fetching more events with filter:', JSON.stringify(filter));
    const result = await useKnowledgeAPI().fetchArticleEvents(props.article.kb_id, {
      page: currEventPage.value + 1,  // API expects page number starting from 1
      start,
      limit,
      filter: filter
    });

    // Append new items to the existing list
    events.value.push(...result.items);
    totalEventCount.value = result.totalCount;
    currEventPage.value += 1;

  } catch (error) {
    console.error('Error fetching article events:', error);
    toast.add({
      severity: 'error',
      summary: t('knowledge.error'),
      detail: t('knowledge.failed_to_fetch_events') || 'Failed to fetch events',
      life: 3000
    });
  } finally {
    loadingEvents.value = false;
  }
};

const shouldShowLoadMore = computed(() => {
  return events.value.length < totalEventCount.value;
});

/**
 * Format events for display in the BravoTimeline component
 */
const formattedEvents = computed(() => {
    return events.value.map((event) => ({
        title: event.type || event.c__d_type || 'Unknown Event',
        user: event.c__users_full_name || 'Unknown User',
        timestamp: getFormattedDate((event.created || Date.now()) as string),
        icon: event.icon || getEventIcon(event.type || event.c__d_type),
        color: event.color || getEventColor(event.type || event.c__d_type),
        content: event.diff_log ? '<html><body>' + event.diff_log + '</body></html>' : '',
    }));
});

/**
 * Get appropriate icon for event type
 */
function getEventIcon(eventType?: string): string {
    if (!eventType) return 'pi pi-info-circle';

    const type = eventType.toLowerCase();

    if (type.includes('update') || type.includes('edit')) return 'pi pi-pencil';
    if (type.includes('create')) return 'pi pi-plus';
    if (type.includes('delete') || type.includes('archive')) return 'pi pi-trash';
    if (type.includes('publish')) return 'pi pi-check-circle';
    if (type.includes('unpublish')) return 'pi pi-times-circle';
    if (type.includes('comment')) return 'pi pi-comment';
    if (type.includes('view') || type.includes('access')) return 'pi pi-eye';
    if (type.includes('search')) return 'pi pi-search';
    if (type.includes('rate')) return 'pi pi-star';
    if (type.includes('share')) return 'pi pi-share-alt';

    return 'pi pi-info-circle';
}

/**
 * Get appropriate color for event type
 */
function getEventColor(eventType?: string): string {
    if (!eventType) return '#607D8B'; // Default gray

    const type = eventType.toLowerCase();

    if (type.includes('create')) return '#4CAF50'; // Green
    if (type.includes('update') || type.includes('edit')) return '#2196F3'; // Blue
    if (type.includes('delete') || type.includes('archive')) return '#F44336'; // Red
    if (type.includes('publish')) return '#8BC34A'; // Light green
    if (type.includes('unpublish')) return '#FF9800'; // Orange
    if (type.includes('comment')) return '#9C27B0'; // Purple
    if (type.includes('view') || type.includes('access')) return '#00BCD4'; // Cyan
    if (type.includes('search')) return '#673AB7'; // Deep purple
    if (type.includes('rate')) return '#FFC107'; // Amber
    if (type.includes('share')) return '#3F51B5'; // Indigo

    return '#607D8B'; // Default gray
}

/**
 * Handle tab change events
 * @param newTab The newly selected tab value
 */
function onTabChange(newTab: string | number): void {
    // Only show filter button when events tab is active
    showFilter.value = newTab === "events";
}

// Variables for sidebar resizing
const sidebarWidth = ref(400); // Default width in pixels
const isResizing = ref(false);

// Computed property to determine if sidebar is in wide mode
const isWideMode = computed(() => {
  return sidebarWidth.value > 450; // If sidebar is wider than 450px, enable wide mode
});

// Watch for article changes to reload events
watch(
    () => props.article?.id,
    (newId) => {
        if (newId) {
            // Reset pagination and fetch events
            currEventPage.value = 0;
            events.value = []; // Clear existing events
            fetchEvents(); // Fetch first page of events
        }
    },
    { immediate: true }
);

// Toggle panel method
const togglePanel = () => {
    emit('toggle-panel');
};

const getNameForId = (id: string) => {
    return userStore.getPartnerLabelById(id) || id || 'Unknown';
};

const getFormattedDate = (date: string) => {
    const dateValue = date || Date.now();
    const theDate = new Date(date || Date.now());
    const dateString = theDate.toLocaleString() + ' (' + formatRelativeTime(dateValue) + ')';
    return dateString;
};

// Fetch comments for this article (all, not just unresolved)
const fetchArticleComments = async () => {
    if (!props.article?.id || !props.article?.root_parent_id) return;
    await knowledgeStore.fetchComments({
        page: 1,
        start: 0,
        limit: 50,
        filter: [
            { property: 'kb_id', value: props.article.kb_id || props.article.id },
            { property: 'root_kb_id', value: props.article.root_parent_id },
            { property: 'type', value: [22], operator: 'in' }
            // No comment_resolved filter!
        ]
    });
};

// Watch for article changes to reload comments
watch(
    () => props.article?.id,
    (newId) => {
        if (newId) {
            fetchArticleComments();
        }
    },
    { immediate: true }
);

const activeComments = computed(() =>
  articleComments.value.filter((c: any) => !c.comment_resolved)
);
const resolvedComments = computed(() =>
  articleComments.value.filter((c: any) => c.comment_resolved)
);

// Add methods for handling resize events
const startResize = (event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();

  // Add a class to the resize handle
  const resizeHandle = event.currentTarget as HTMLElement;
  resizeHandle.classList.add('resizing');

  // Add a transparent overlay that covers the whole document to capture mouse events
  const overlay = document.createElement('div');
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100%';
  overlay.style.height = '100%';
  overlay.style.backgroundColor = 'transparent';
  overlay.style.zIndex = '9999';
  overlay.style.cursor = 'col-resize';
  document.body.appendChild(overlay);

  // Store initial position
  const startX = event.clientX;
  const startWidth = sidebarWidth.value;
  document.body.classList.add('resizing');

  // Add mousemove event listener to the overlay
  const onMouseMove = (e: MouseEvent) => {
    // Invert the calculation for more intuitive direction
    // For right-side sidebar, we need to invert the deltaX
    // When dragging left (negative deltaX), sidebar should get larger
    // When dragging right (positive deltaX), sidebar should get smaller
    const deltaX = startX - e.clientX;
    let newWidth = startWidth + deltaX;

    // Add min/max constraints
    newWidth = Math.max(newWidth, 280); // Min width
    newWidth = Math.min(newWidth, 600); // Max width

    sidebarWidth.value = newWidth;
  };

  // Add mouseup event listener to the overlay
  const onMouseUp = () => {
    // Remove the resizing class
    resizeHandle.classList.remove('resizing');

    document.body.classList.remove('resizing');
    document.body.removeEventListener('mousemove', onMouseMove);
    document.body.removeEventListener('mouseup', onMouseUp);
    document.body.removeChild(overlay);

    // Persist the width to local storage or your state management
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('article-sidebar-width', String(sidebarWidth.value));
    }
  };

  document.body.addEventListener('mousemove', onMouseMove);
  document.body.addEventListener('mouseup', onMouseUp);
};

// Clean up event listeners when component is unmounted
onUnmounted(() => {
  document.body.classList.remove('resizing');

  // Remove the overlay if it exists
  const overlay = document.getElementById('resize-overlay');
  if (overlay) {
    document.body.removeChild(overlay);
  }
});

// Handle field updates from the information block
const handleFieldUpdate = (field: string, value: string) => {
    console.log(`Field ${field} updated to: ${value}`);
    // Forward the update to parent component
    emit('update:article', field, value);
};

// Handle update event from ArticleInformationBlock and re-emit upwards
const handleInfoBlockUpdate = () => {
    emit('update');
};

const handleResolveSidebarComment = async (commentId: string) => {
    try {
        resolvingCommentIds.value.push(commentId);
        await knowledgeStore.resolveComment(commentId);
        toast.add({
            severity: 'success',
            summary: t('knowledge.success'),
            detail: t('knowledge.comment_resolved'),
            life: 3000
        });
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: t('knowledge.error'),
            detail: t('knowledge.failed_to_resolve_comment'),
            life: 3000
        });
    } finally {
        resolvingCommentIds.value = resolvingCommentIds.value.filter(id => id !== commentId);
    }
};

const showAddComment = () => {
  addingComment.value = true;
  newComment.value = '';
};
const cancelAddComment = () => {
  addingComment.value = false;
  newComment.value = '';
};
const knowledgeApiInstance = useKnowledgeApiInstance();
const submitAddComment = async () => {
  if (!newComment.value.trim()) return;
  submittingComment.value = true;
  try {
    await knowledgeApiInstance.addComment({
      kb_id: props.article.kb_id || props.article.id,
      root_kb_id: props.article.root_parent_id,
      notes: newComment.value.trim()
    });
    toast.add({
      severity: 'success',
      summary: t('knowledge.success'),
      detail: t('knowledge.comment_added') || 'Comment added',
      life: 3000
    });
    addingComment.value = false;
    newComment.value = '';
    await fetchArticleComments();
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: t('knowledge.error'),
      detail: t('knowledge.failed_to_add_comment') || 'Failed to add comment',
      life: 3000
    });
  } finally {
    submittingComment.value = false;
  }
};
</script>

<template>
    <div
        :class="['article-sidebar', { minimized: isPanelMinimized }]"
        data-testid="article-sidebar"
        :style="!isPanelMinimized ? `width: ${sidebarWidth}px; min-width: ${sidebarWidth}px; max-width: ${sidebarWidth}px;` : ''"
    >
        <!-- Add drag handle for resizing with visible grabber -->
        <div
            v-if="!isPanelMinimized"
            class="resize-handle"
            :class="{ 'resizing': isResizing }"
            @mousedown="startResize"
            data-testid="sidebar-resize-handle"
        >
            <div class="resize-grabber"></div>
        </div>

        <div class="sidebar-header" data-testid="sidebar-header">
            <BravoTitlePage data-testid="sidebar-title">{{ t('nav.settings') }}</BravoTitlePage>
            <BravoButton
                v-if="!isPanelMinimized"
                icon="pi pi-chevron-right"
                text
                severity="secondary"
                class="minimize-button"
                :class="{ rotated: isPanelMinimized }"
                @click="togglePanel"
                aria-label="Toggle panel"
                v-tooltip.bottom="{ value: t('common.collapse'), showDelay: 400 }"
                data-testid="minimize-sidebar-button"
            />
        </div>

        <!-- Button visible only when minimized -->
        <BravoButton
            v-if="isPanelMinimized"
            icon="pi pi-chevron-right"
            text
            severity="secondary"
            class="minimize-button minimized-button"
            :class="{ rotated: isPanelMinimized }"
            @click="togglePanel"
            aria-label="Toggle panel"
            v-tooltip.bottom="{ value: t('common.expand'), showDelay: 400 }"
            data-testid="expand-sidebar-button"
        />

        <div class="sidebar-tabs" data-testid="sidebar-tabs">
            <Tabs value="details" class="sidebar-tabs" @update:value="onTabChange">
                <TabList>
                    <Tab value="details" data-testid="details-tab">{{ t('common.details') }}</Tab>
                    <Tab value="events" data-testid="events-tab">{{ t('common.events') }}</Tab>
                    <Tab value="comments" data-testid="comments-tab">{{ t('common.comments') }}</Tab>
                </TabList>
                <div class="filter-refresh-container" v-if="showFilter">
                    <FilterBtn
                        @update:filters="onFilterChange"
                        @popover:hide="fetchEventsWithFilters(currentFilterIndexes)"
                        @popover:show="() => {}"
                    />
                    <BravoButton
                        icon="pi pi-refresh"
                        class="refresh-button"
                        severity="secondary"
                        @click="refreshEvents"
                        aria-label="Refresh events"
                        v-tooltip.bottom="{ value: t('knowledge.refresh'), showDelay: 400 }"
                        data-testid="refresh-events-button"
                    />
                </div>
                <TabPanels>
                    <TabPanel value="details" data-testid="details-panel">
                        <div class="sidebar-section" data-testid="sidebar-section-details">
                            <BravoBlock :multiple="true" :value="['0']" data-testid="details-accordion">
                                <ArticleInformationBlock
                                    value="0"
                                    :article="article"
                                    :isLoading="isLoading"
                                    :isWideMode="isWideMode"
                                    :isReady="!isLoading"
                                    @update:field="handleFieldUpdate"
                                    @update="handleInfoBlockUpdate"
                                />

                                <BravoAccordionPanel value="1" data-testid="system-info-panel">
                                    <BravoAccordionHeader data-testid="system-info-header">{{
                                        t('knowledge.system_information')
                                    }}</BravoAccordionHeader>
                                    <BravoAccordionContent data-testid="system-info-content">
                                        <div v-if="isLoading" class="skeleton-content">
                                            <Skeleton height="1.5rem" class="mb-2" />
                                            <Skeleton height="1.5rem" class="mb-2" />
                                            <Skeleton height="1.5rem" class="mb-2" />
                                            <Skeleton height="1.5rem" class="mb-2" />
                                        </div>
                                        <div v-else class="data-fields" data-testid="system-info-fields">
                                            <div class="data-field" :class="{ 'horizontal': isWideMode }" data-testid="article-created-field">
                                                <label>Created</label>
                                                <div class="field-value" data-testid="article-created-value">
                                                    {{ article.created ? formatDate(article.created) : 'Unknown' }}
                                                </div>
                                            </div>
                                            <div class="data-field" :class="{ 'horizontal': isWideMode }" data-testid="article-creator-field">
                                                <label>Created by</label>
                                                <div class="field-value" data-testid="article-creator-value">
                                                    <i class="pi pi-user" />
                                                    {{ getNameForId(article.creator_user_id) }}
                                                </div>
                                            </div>
                                            <div
                                                class="data-field"
                                                :class="{ 'horizontal': isWideMode }"
                                                v-if="article.c__contributor_user_ids?.length"
                                                data-testid="article-contributors-field"
                                            >
                                                <label>Contributors</label>
                                                <div class="field-value" data-testid="article-contributors-value">
                                                    <div
                                                        v-for="contributor in article.c__contributor_user_ids"
                                                        :key="contributor"
                                                        :data-testid="`article-contributor-${contributor}`"
                                                    >
                                                        <i class="pi pi-user" />
                                                        {{ getNameForId(contributor) }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="data-field" :class="{ 'horizontal': isWideMode }" data-testid="article-updated-field">
                                                <label>Last updated</label>
                                                <div class="field-value" data-testid="article-updated-value">
                                                    {{
                                                        article.updated
                                                            ? formatRelativeTime(article.updated)
                                                            : 'Unknown'
                                                    }}
                                                </div>
                                            </div>
                                            <div
                                                class="data-field"
                                                :class="{ 'horizontal': isWideMode }"
                                                v-if="article.c__last_edited"
                                                data-testid="article-last-editor-field"
                                            >
                                                <label>Last edited by</label>
                                                <div class="field-value" data-testid="article-last-editor-value">
                                                    <i class="pi pi-user" />
                                                    {{ getNameForId(article.c__contributor_user_ids?.[0]) }}
                                                </div>
                                            </div>
                                        </div>
                                    </BravoAccordionContent>
                                </BravoAccordionPanel>

                            </BravoBlock>
                        </div>
                    </TabPanel>
                    <TabPanel value="events" data-testid="events-panel">
                        <div :class="shouldShowLoadMore ? 'scrollable-panel' : 'scrollable-panel-full'">
                            <!-- Loading skeletons -->
                            <div v-if="isLoading" class="skeleton-content">
                                <Skeleton height="3rem" class="mb-2" />
                                <Skeleton height="3rem" class="mb-2" />
                                <Skeleton height="3rem" class="mb-2" />
                                <Skeleton height="3rem" class="mb-2" />
                            </div>

                            <!-- Loading spinner -->
                            <div v-else-if="loadingEvents && events.length === 0" class="loading-container" data-testid="events-loading">
                                <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
                                <p>{{ t('common.loading') }}</p>
                            </div>

                            <!-- Empty state -->
                            <div
                                v-else-if="formattedEvents.length === 0"
                                class="empty-state"
                                data-testid="events-empty-state"
                            >
                                <p>{{ t('knowledge.no_events') }}</p>
                            </div>

                            <!-- Timeline of events -->
                            <BravoTimeline 
                                :value="formattedEvents" 
                                :useIcons="true"
                                class="events-timeline"
                                data-testid="events-timeline"
                            >
                                <template #item-content="{ item }">
                                    <BravoBody v-html="item.content"></BravoBody>
                                </template>
                            </BravoTimeline>

                            <!-- Load more button -->
                            <BravoButton
                                v-if="shouldShowLoadMore"
                                class="showloadmore"
                                :label="t('knowledge.load_more') || 'Load More Events'"
                                :loading="loadingEvents && events.length > 0"
                                :disabled="loadingEvents"
                                @click="fetchEvents"
                                text
                            />
                        </div>
                    </TabPanel>
                    <TabPanel value="comments" data-testid="comments-panel">
                        <div class="add-comment-bar">
                          <BravoButton
                            v-if="!addingComment"
                            class="add-comment-btn"
                            :label="t('knowledge.add_comment') || 'Add Comment'"
                            icon="pi pi-plus"
                            @click="showAddComment"
                          />
                          <div v-else class="add-comment-form">
                            <BravoTextarea v-model="newComment" rows="3" autoResize :disabled="submittingComment" />
                            <div class="add-comment-actions">
                              <BravoButton
                                class="cancel-btn"
                                :label="t('common.cancel')"
                                severity="secondary"
                                @click="cancelAddComment"
                                :disabled="submittingComment"
                              />
                              <BravoButton
                                class="submit-btn"
                                :label="t('common.save')"
                                :loading="submittingComment"
                                @click="submitAddComment"
                                :disabled="submittingComment || !newComment.trim()"
                              />
                            </div>
                          </div>
                        </div>
                        <div v-if="isLoading || loadingArticleComments" class="skeleton-content">
                            <Skeleton height="4rem" class="mb-3" />
                            <Skeleton height="4rem" class="mb-3" />
                            <Skeleton height="4rem" class="mb-3" />
                        </div>
                        <div v-else-if="activeComments.length === 0" class="empty-state">
                            <p>{{ t('knowledge.no_comments') }}</p>
                        </div>
                        <div v-else class="comments-container" data-testid="comments-container">
                            <BravoComment
                                v-for="(comment, index) in activeComments"
                                :key="comment.id"
                                :commenterName="(comment as any).c__users_full_name || (comment as any).c__users_nickname || t('knowledge.unknown_user')"
                                :commenterAvatar="(comment as any).user_avatar || undefined"
                                :commentDate="new Date((comment as any).created || '')"
                                :articleTitle="(comment as any).kb_title || ''"
                                :articleUrl="`/knowledge/articles/${(comment as any).kb_id}`"
                                :commentBody="(comment as any).notes || (comment as any).diff_log || (comment as any).c__diff_log || ''"
                                :showResolveButton="!(comment as any).comment_resolved"
                                :loading="resolvingCommentIds.includes(comment.id)"
                                :showArticleURL="false"
                                @resolve="handleResolveSidebarComment(comment.id)"
                                :data-testid="`comment-${index}`"
                            />
                        </div>
                        <div class="resolved-comments-section" v-if="resolvedComments.length">
                            <button class="resolved-toggle" @click="showResolved = !showResolved">
                                {{ showResolved ? t('knowledge.hide_resolved_comments') : t('knowledge.show_resolved_comments') }} ({{ resolvedComments.length }})
                            </button>
                            <div v-show="showResolved" class="comments-container resolved">
                                <BravoComment
                                    v-for="(comment, index) in resolvedComments"
                                    :key="comment.id"
                                    :commenterName="(comment as any).c__users_full_name || (comment as any).c__users_nickname || t('knowledge.unknown_user')"
                                    :commenterAvatar="(comment as any).user_avatar || undefined"
                                    :commentDate="new Date((comment as any).created || '')"
                                    :articleTitle="(comment as any).kb_title || ''"
                                    :articleUrl="`/knowledge/articles/${(comment as any).kb_id}`"
                                    :commentBody="(comment as any).notes || (comment as any).diff_log || (comment as any).c__diff_log || ''"
                                    :showResolveButton="false"
                                    :showArticleURL="false"
                                    :data-testid="`resolved-comment-${index}`"
                                />
                            </div>
                        </div>
                    </TabPanel>
                </TabPanels>
            </Tabs>
        </div>
    </div>
</template>

<style scoped>
.article-sidebar {
    position: relative;
    flex-shrink: 0;
    background: white;
    border-left: 1px solid var(--border-color);
    transition: width 0.1s ease-out;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100vh;
}

.article-sidebar.minimized {
    width: 52px;
    min-width: 52px;
    padding: 0;
}

.article-sidebar.minimized .sidebar-header,
.article-sidebar.minimized .sidebar-section,
.article-sidebar.minimized .p-divider,
.article-sidebar.minimized .tabs-container {
    display: none;
}

.article-sidebar > *:not(.sidebar-header):not(.minimize-button) {
    padding: 0;
}

.minimize-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    transition: transform 0.3s ease;
    z-index: 20;
}

.minimized-button {
    position: absolute;
    left: 8px;
    top: 16px;
    background: white;
    border: 1px solid var(--border-color);
}

.minimize-button.rotated {
    transform: rotate(180deg);
}

.minimize-button:hover {
    background: var(--surface-100);
}

.article-sidebar.minimized .sidebar-tabs,
.article-sidebar.minimized .sidebar-header {
    display: none;
}

.sidebar-header {
    height: 64px;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    background-color: white;
    flex-shrink: 0;
}

.sidebar-tabs {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
    height: calc(100vh - 64px); /* Subtract header height */
}

.sidebar-tabs :deep(.p-tablist) {
    padding: 1rem 1.5rem 0rem 1.5rem;
    flex-shrink: 0;
    background: white;
    z-index: 1;
}

.sidebar-tabs :deep(.p-tabpanels) {
    overflow: auto;
    height: 100%;
}

.sidebar-tabs :deep(.p-tabpanel) {
    height: 100%;
    padding: 0 1.5rem;
    flex-grow: 1;
}

.data-fields {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.data-field label {
    font-size: 0.75rem;
    color: #64748b;
    display: block;
    margin-bottom: 0.25rem;
}

.data-field .field-value {
    font-size: 0.813rem;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.data-field .field-value i {
    color: #64748b;
    font-size: 1rem;
}

.related-articles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-article {
    padding: 1rem;
    background: white;
}

.related-article h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
}

.related-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #64748b;
}

.empty-state {
    color: #64748b;
    font-style: italic;
    padding: 1rem 0;
}

.events-timeline {
    margin-top: 1rem;
    padding: 0 0 1rem 0;
}

.events-timeline :deep(.p-timeline-event-opposite) {
    display: none;
}

.events-timeline :deep(.p-timeline-event-content) {
    margin-left: 1rem;
}

.event-content {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.event-tag {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.event-tag i {
    font-size: 1rem;
}

.event-status {
    font-weight: 500;
    color: #1e293b;
}

.event-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.event-user {
    font-size: 0.875rem;
    color: #64748b;
}

.event-date {
    font-size: 0.75rem;
    color: #94a3b8;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background-color: var(--surface-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.search-terms {
    color: #64748b;
    font-size: 0.813rem;
    line-height: 1.4;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #64748b;
}

.loading-container i {
    margin-bottom: 1rem;
}

.event-description {
    font-size: 0.813rem;
    color: #1e293b;
    margin-top: 0.5rem;
    line-height: 1.4;
    white-space: pre-line;
}

/* Comments styles */
.comments-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
}

:deep(.bravo-comment) {
    margin-bottom: 0.5rem;
}

.skeleton-content {
    padding: 0.5rem 0;
}

.skeleton-content .p-skeleton {
    margin-bottom: 0.75rem;
}

.resize-handle {
    position: absolute;
    left: -8px;
    top: 0;
    width: 16px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: col-resize;
    z-index: 10;
}

.resize-grabber {
    width: 4px;
    height: 60px;
    border-radius: 2px;
    background-color: #ddd;
    position: relative;
    transition: background-color 0.2s;
}

.resize-handle:hover .resize-grabber,
.resize-handle.resizing .resize-grabber {
    background-color: var(--primary-color, #3B82F6);
}

.resize-handle:hover .resize-grabber::before,
.resize-handle.resizing .resize-grabber::before {
    color: white;
}

/* Add style for the body while resizing */
:global(body.resizing) {
    cursor: col-resize !important;
    user-select: none;
}

/* Responsive layout styles */
.data-field {
    display: flex;
    flex-direction: column;
}

.data-field.horizontal {
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0.5rem;
}

.data-field.horizontal label {
    flex: 0 0 25%;
    margin-bottom: 0;
    padding-top: 0.25rem;
}

.data-field.horizontal .field-value {
    flex: 0 0 70%;
}

/* Ensure smooth transition when resizing */
.sidebar-section, .data-field {
    transition: all 0.1s ease;
}

/* Add styles for resolved comments section */
.resolved-comments-section {
  margin-top: 2rem;
}
.resolved-toggle {
  background: none;
  border: none;
  color: var(--primary-color, #3B82F6);
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  padding: 0;
}
.comments-container.resolved {
  opacity: 0.7;
}

/* Add styles for add comment UI */
.add-comment-bar {
  margin-bottom: 1.5rem;
}


.add-comment-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.add-comment-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
}
.spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid #fff;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  vertical-align: middle;
}
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Scrollable panel for events */
.scrollable-panel {
  max-height: 77vh;
  overflow-y: auto;
  padding-bottom: 10px;
  border-radius: 4px;
}

.scrollable-panel-full {
  max-height: 77vh;
  overflow-y: auto;
  padding-bottom: 10px;
  border-radius: 4px;
}

/* Load more button styling */
.showloadmore {
  width: 100%;
  margin-top: 10px;
  justify-content: center;
}

/* Filter and refresh buttons container */
.filter-refresh-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1.5rem;
  margin-top: 0.5rem;
}

.refresh-button {
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: var(--surface-200);
}

/* Timeline content styling */
:deep(.timeline-content .body *) {
  font-size: 12px !important;
  line-height: 1.4;
}

/* Timeline event styling */
:deep(.bravo-timeline-event) {
  margin-bottom: 12px;
}

:deep(.bravo-timeline-event-header) {
  font-weight: 500;
}
</style>
