<script setup lang="ts">
import { onMounted, ref } from 'vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import { useCasesStore } from '../../../stores/cases'
import BravoInboxSidebar from '../components/BravoInboxSidebar.vue'
import ViewCard from '../../../components/ViewCard.vue'
import BravoEventsList from '../../../components/BravoEventsList.vue'
import BravoRightPanel from '../../../components/BravoRightPanel.vue'
import IssueDetails from '../components/IssueDetails.vue'
import type { SidebarItem } from '../components/BravoInboxSidebar.vue'
import type { Issue } from '../../../services/IssuesAPI'

const casesStore = useCasesStore()
const selectedView = ref<SidebarItem | null>(null)
const showingEvents = ref(false)
const showingIssue = ref(false)

// Debug computed property to check if button should be visible
// const shouldShowEventsButton = computed(() => {
//   const result = showingIssue.value || (casesStore.currentIssue !== null)
//   console.log('Should show events button:', result, 
//     'showingIssue:', showingIssue.value, 
//     'currentIssue:', casesStore.currentIssue)
//   return result
// })

onMounted(async () => {
  
  // Fetch views and cases first since they don't depend on the current issue
  await casesStore.fetchViews();
  await casesStore.fetchCases({ page: 1, limit: 25 });
  
  // Fetch the current issue last
  if (casesStore.issueId) {
    await casesStore.fetchCurrentIssue(casesStore.issueId);
    console.log('After fetchCurrentIssue:', casesStore.currentIssue);
  }
})

// Handle sidebar item selection
const handleSelectItem = (item: SidebarItem) => {
  selectedView.value = item
  showingEvents.value = false
  showingIssue.value = false // Hide issue details when switching views
}

// Toggle events view
const toggleEventsView = () => {
  showingEvents.value = !showingEvents.value
  showingIssue.value = false // Hide issue details when showing events
}

// Handle issue selection
const handleSelectIssue = async (issue: Issue) => {
  // Update the current issue in the store
  await casesStore.fetchCurrentIssue(issue.id)
  showingIssue.value = true // Show issue details
  console.log('After selecting issue:', casesStore.currentIssue)
}

// Debug function
const debugStoreState = () => {
  console.log('Debug - Store state:', {
    currentIssue: casesStore.currentIssue,
    showingIssue: showingIssue.value,
    showingEvents: showingEvents.value
  })
  
  alert(`Current issue: ${casesStore.currentIssue ? 'YES' : 'NO'}\nShowing issue: ${showingIssue.value}\nShowing events: ${showingEvents.value}`)
}

// Back function for the IssueDetails component
const handleBack = () => {
  showingIssue.value = false
}
</script>

<template>
  <div class="inbox-view">
    <div class="inbox-header">
      <BravoTitle1>Inbox</BravoTitle1>
      <div class="header-actions">
        <BravoButton 
          :label="showingEvents ? 'Hide Events' : 'Show Events'" 
          icon="pi pi-list" 
          class="events-button"
          severity="primary"
          @click="toggleEventsView"
        />
        <BravoButton 
          label="Debug" 
          icon="pi pi-info-circle"
          severity="help" 
          @click="debugStoreState"
        />
      </div>
    </div>
    <div class="inbox-content">
      <BravoInboxSidebar @select-item="handleSelectItem" />
      <div class="main-content">
        <!-- Show interaction events if enabled -->
        <template v-if="showingEvents && casesStore.currentIssue">
          <div class="current-issue-header">
            <h2>Events for Issue: {{ casesStore.currentIssue.id }}</h2>
          </div>
          <BravoEventsList :issueId="casesStore.currentIssue.id" />
        </template>
        
        <!-- Show issue details if an issue is selected -->
        <IssueDetails 
          v-else-if="showingIssue" 
          :onBack="handleBack"
          :onViewEvents="toggleEventsView"
        />
        
        <!-- Otherwise show the view card -->
        <ViewCard 
          v-else 
          :selected-view="selectedView" 
          @select-issue="handleSelectIssue"
        />
      </div>
      
      <!-- Right Panel -->
      <BravoRightPanel v-if="casesStore.currentIssue" :issue="casesStore.currentIssue" />
    </div>
  </div>
</template>

<style scoped>
.inbox-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.inbox-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--surface-200);
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.back-link {
  margin-bottom: 1rem;
  color: var(--primary-color);
  cursor: pointer;
  display: inline-block;
}

.back-link:hover {
  text-decoration: underline;
}

.events-toggle-btn {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.events-toggle-btn:hover {
  background-color: var(--primary-600);
}

.current-issue-header {
  margin-bottom: 1rem;
}

.current-issue-header h2 {
  font-size: 1.25rem;
  color: var(--surface-900);
  margin: 0;
}

.inbox-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--surface-50);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
}

.error-container {
  color: var(--red-500);
  padding: 1rem;
  border-radius: 4px;
  background-color: var(--red-50);
}

.issue-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.issue-field {
  margin-bottom: 0.75rem;
}

.issue-field:last-child {
  margin-bottom: 0;
}

.issue-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  color: var(--surface-600);
  padding: 2rem;
}
</style> 