<script setup lang="ts">
import { computed } from 'vue'
import { useCasesStore } from '../../../stores/cases'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'

// Define props
interface Props {
  issueId?: string
  onBack?: () => void
  onViewEvents?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  issueId: undefined,
  onBack: () => {},
  onViewEvents: () => {}
})

const casesStore = useCasesStore()

// Use current issue from the store if no specific issue ID is provided
const issue = computed(() => {
  if (props.issueId) {
    // If we have an issueId prop, we should fetch and display that
    if (casesStore.currentIssue?.id !== props.issueId) {
      casesStore.fetchCurrentIssue(props.issueId)
    }
  }
  
  return casesStore.currentIssue
})

const goBack = () => {
  props.onBack()
}

const viewEvents = () => {
  props.onViewEvents()
}
</script>

<template>
  <div class="issue-details-container">
    <div v-if="casesStore.loading" class="loading-state">
      <div class="spinner"></div>
      <span>Loading issue details...</span>
    </div>
    
    <div v-else-if="casesStore.error" class="error-state">
      <p>{{ casesStore.error }}</p>
    </div>
    
    <div v-else-if="issue" class="issue-content">
      <div class="back-link" @click="goBack">
        &larr; Back
      </div>
      
      <div class="issue-header">
        <h2>Issue Details: {{ issue.display_name || issue.id }}</h2>
      </div>
      
      <div class="issue-card">
        <div class="issue-field">
          <strong>ID:</strong> {{ issue.id }}
        </div>
        <div v-if="issue.source_data" class="issue-field">
          <strong>Source:</strong> {{ issue.source_data.source }}
        </div>
        <div class="issue-field">
          <strong>Available Communications:</strong> 
          {{ issue.availableComm ? issue.availableComm.length : 0 }}
        </div>
        <div class="issue-actions">
          <BravoButton 
            label="View Events" 
            icon="pi pi-list"
            size="small" 
            @click="viewEvents"
          />
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>No issue selected</p>
    </div>
  </div>
</template>

<style scoped>
.issue-details-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  flex: 1;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--surface-200);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-state {
  padding: 1.5rem;
  color: var(--red-600);
  background-color: var(--red-50);
  border-radius: 4px;
  margin: 1rem;
}

.issue-content {
  padding: 1.5rem;
}

.back-link {
  display: inline-block;
  color: var(--primary-color);
  cursor: pointer;
  margin-bottom: 1rem;
}

.back-link:hover {
  text-decoration: underline;
}

.issue-header {
  margin-bottom: 1.5rem;
}

.issue-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--surface-900);
}

.issue-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--surface-200);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.issue-field {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.issue-field:last-of-type {
  margin-bottom: 1.5rem;
}

.issue-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--surface-600);
  padding: 2rem;
}
</style> 