/**
 * Froala Editor Plugin for KB Article Insertion
 *
 * This plugin allows users to:
 * - Insert KB articles as links or embedded content
 * - Edit existing KB article references
 * - Preview and interact with embedded articles
 */

import FroalaEditor from "froala-editor";
import { getHttpClient } from '../../../../services/httpClientProvider';
import { useAuthStore } from '../../../../stores/auth';
import { useKnowledgeAPI } from '../../../../composables/services/useKnowledgeAPI';
import './kbArticle.css';

// Register popup templates
FroalaEditor.POPUP_TEMPLATES["kbArticle.insert"] =
  "[_KB_SELECTION_LAYER_]";
FroalaEditor.POPUP_TEMPLATES["kbArticle.edit"] = "[_BUTTONS_]";

// Default configuration for the plugin
FroalaEditor.DEFAULTS.kbArticleEditButtons = [
  "kbArticleOpen",
  "kbArticleRemove",
];

// Define icons for buttons
FroalaEditor.DefineIcon("insertKbArticle", {
  NAME: "newspaper",
  SVG_KEY: "article",
});

FroalaEditor.DefineIconTemplate('kbArticleRemove_tpl', '<i style="color: #626363;" class="icon icon-16 icon-unlink"></i>');
FroalaEditor.DefineIcon('kbArticleRemove', { template: 'kbArticleRemove_tpl' });

FroalaEditor.DefineIconTemplate('kbArticleEdit_tpl', '<i style="color: #626363;" class="icon icon-16 icon-edit"></i>');
FroalaEditor.DefineIcon('kbArticleEdit', { template: 'kbArticleEdit_tpl' });

FroalaEditor.DefineIconTemplate('kbArticleOpen_tpl', '<i style="color: #626363;" class="icon icon-16 icon-arrow-right"></i>');
FroalaEditor.DefineIcon('kbArticleOpen', { template: 'kbArticleOpen_tpl' });

// Main plugin definition
const kbArticle = function (editor) {
  // Initialize auth store and get HTTP client
  const authStore = useAuthStore();
  const httpClient = getHttpClient(authStore);
  // Initialize knowledge API
  const knowledgeAPI = useKnowledgeAPI();

  // Check if an element is a KB article block
  function isKbArticleBlock(el) {
    return (
      el &&
      ["SPAN", "DIV"].includes(el.tagName) &&
      el.dataset.btBlock === "bt-article"
    );
  }

  // Get current KB article under cursor
  function get() {
    if (editor.$wp) {
      // Check common ancestor
      let commonAncestor = editor.selection.ranges(0).commonAncestorContainer;

      try {
        if (
          commonAncestor &&
          ((commonAncestor.contains && commonAncestor.contains(editor.el)) ||
            !editor.el.contains(commonAncestor) ||
            editor.el === commonAncestor)
        ) {
          commonAncestor = null;
        }
      } catch (ex) {
        commonAncestor = null;
      }

      if (commonAncestor && isKbArticleBlock(commonAncestor)) {
        return commonAncestor;
      }

      // Check selection elements
      let startElement = editor.selection.element();
      let endElement = editor.selection.endElement();

      if (
        !isKbArticleBlock(startElement) &&
        !editor.node.isElement(startElement)
      ) {
        startElement = editor
          .$(startElement)
          .parentsUntil(
            editor.$el,
            'div[data-bt-block="bt-article"],span[data-bt-block="bt-article"]'
          )
          .get(0);
      }

      if (!isKbArticleBlock(endElement) && !editor.node.isElement(endElement)) {
        endElement = editor
          .$(endElement)
          .parentsUntil(
            editor.$el,
            'div[data-bt-block="bt-article"],span[data-bt-block="bt-article"]'
          )
          .get(0);
      }

      // Check for validity of elements
      try {
        if (
          endElement &&
          ((endElement.contains && endElement.contains(editor.el)) ||
            !editor.el.contains(endElement) ||
            editor.el === endElement)
        ) {
          endElement = null;
        }
      } catch (ex) {
        endElement = null;
      }

      try {
        if (
          startElement &&
          ((startElement.contains && startElement.contains(editor.el)) ||
            !editor.el.contains(startElement) ||
            editor.el === startElement)
        ) {
          startElement = null;
        }
      } catch (ex) {
        startElement = null;
      }

      if (
        endElement &&
        endElement === startElement &&
        isKbArticleBlock(endElement)
      ) {
        // Check for edge clicks in IE/mobile
        if (
          (editor.browser.msie || editor.helpers.isMobile()) &&
          (editor.selection.info(startElement).atEnd ||
            editor.selection.info(startElement).atStart)
        ) {
          return null;
        }

        return startElement;
      }
    } else if (isKbArticleBlock(editor.el)) {
      return editor.el;
    }

    return null;
  }

  // Find all KB article blocks in selection
  function allSelected() {
    const selectedBlocks = [];
    let range;
    let containerEl;
    let blocks;
    let blockRange;

    if (editor.win.getSelection) {
      const sel = editor.win.getSelection();

      if (sel.getRangeAt && sel.rangeCount) {
        blockRange = editor.doc.createRange();

        for (let r = 0; r < sel.rangeCount; ++r) {
          range = sel.getRangeAt(r);
          containerEl = range.commonAncestorContainer;

          if (containerEl && containerEl.nodeType !== 1) {
            containerEl = containerEl.parentNode;
          }

          if (containerEl && isKbArticleBlock(containerEl)) {
            selectedBlocks.push(containerEl);
          } else {
            blocks = [
              containerEl.getElementsByTagName("div"),
              containerEl.getElementsByTagName("span"),
            ];

            blocks.forEach((elements) => {
              for (let i = 0; i < elements.length; ++i) {
                if (isKbArticleBlock(elements[i])) {
                  blockRange.selectNodeContents(elements[i]);
                  if (
                    blockRange.compareBoundaryPoints(
                      range.END_TO_START,
                      range
                    ) < 1 &&
                    blockRange.compareBoundaryPoints(
                      range.START_TO_END,
                      range
                    ) > -1
                  ) {
                    selectedBlocks.push(elements[i]);
                  }
                }
              }
            });
          }
        }
      }
    } else if (
      editor.doc.selection &&
      editor.doc.selection.type !== "Control"
    ) {
      range = editor.doc.selection.createRange();
      containerEl = range.parentElement();

      if (isKbArticleBlock(containerEl)) {
        selectedBlocks.push(containerEl);
      } else {
        blocks = [
          containerEl.getElementsByTagName("div"),
          containerEl.getElementsByTagName("span"),
        ];

        blockRange = editor.doc.body.createTextRange();

        blocks.forEach((elements) => {
          for (let j = 0; j < elements.length; ++j) {
            if (isKbArticleBlock(elements[j])) {
              blockRange.moveToElementText(elements[j]);

              if (
                blockRange.compareEndPoints("StartToEnd", range) > -1 &&
                blockRange.compareEndPoints("EndToStart", range) < 1
              ) {
                selectedBlocks.push(elements[j]);
              }
            }
          }
        });
      }
    }

    return selectedBlocks;
  }

  // Edit the KB article block
  function _edit(e) {
    if (editor.core.hasFocus()) {
      _hideEditPopup();

      // Skip when ALT key is pressed
      if (
        e &&
        e.type === "keyup" &&
        (e.altKey || e.which === FroalaEditor.KEYCODE.ALT)
      ) {
        return true;
      }

      setTimeout(
        function () {
          if (!e || (e && (e.which === 1 || e.type !== "mouseup"))) {
            const block = get();
            if (block) {
              if (e) {
                e.stopPropagation();
              }
              _showEditPopup(block);
            }
          }
        },
        editor.helpers.isIOS() ? 100 : 0
      );
    }
  }

  // Show edit popup for KB article
  function _showEditPopup(block) {
    let $popup = editor.popups.get("kbArticle.edit");
    if (!$popup) {
      $popup = _initEditPopup();
    }

    const $block = editor.$(block);

    if (!editor.popups.isVisible("kbArticle.edit")) {
      editor.popups.refresh("kbArticle.edit");
    }

    editor.popups.setContainer("kbArticle.edit", editor.$sc);

    const left = $block.offset().left + $block.outerWidth() / 4;
    const top = $block.offset().top + $block.outerHeight();

    editor.popups.show("kbArticle.edit", left, top, $block.outerHeight());
  }

  // Hide edit popup
  function _hideEditPopup() {
    editor.popups.hide("kbArticle.edit");
  }

  // Initialize the edit popup
  function _initEditPopup() {
    let buttonsHtml = "";

    if (editor.opts.kbArticleEditButtons.length >= 1) {
      if (
        isKbArticleBlock(editor.el) &&
        editor.opts.kbArticleEditButtons.indexOf("kbArticleRemove") >= 0
      ) {
        editor.opts.kbArticleEditButtons.splice(
          editor.opts.kbArticleEditButtons.indexOf("kbArticleRemove"),
          1
        );
      }

      buttonsHtml =
        '<div class="fr-buttons">' +
        editor.button.buildList(editor.opts.kbArticleEditButtons) +
        "</div>";
    }

    const template = {
      buttons: buttonsHtml,
    };

    // Create the popup
    const $popup = editor.popups.create("kbArticle.edit", template);

    if (editor.$wp) {
      editor.events.$on(editor.$wp, "scroll.kbArticle-edit", function () {
        if (get() && editor.popups.isVisible("kbArticle.edit")) {
          _showEditPopup(get());
        }
      });
    }

    return $popup;
  }

  function isElementVisible(el) {
    if (!el) return false;

    const style = window.getComputedStyle(el);
    return (
      style.display !== "none" &&
      style.visibility !== "hidden" &&
      style.opacity !== "0" &&
      el.offsetWidth > 0 &&
      el.offsetHeight > 0
    );
  }

  // Show insert popup for KB article
  function _showInsertPopup() {
    const $btn = editor.$tb.find('.fr-command[data-cmd="insertKbArticle"]');
    let $popup = editor.popups.get("kbArticle.insert");

    if (!$popup) {
      $popup = _initInsertPopup();
    }

    if (!$popup.hasClass("fr-active")) {
      editor.popups.refresh("kbArticle.insert");
      editor.popups.setContainer("kbArticle.insert", editor.$tb || editor.$sc);

      if ($btn.length && isElementVisible($btn[0])) {
        const left = $btn.offset().left + $btn.outerWidth() / 2;
        const top =
          $btn.offset().top +
          (editor.opts.toolbarBottom ? 10 : $btn.outerHeight() - 10);

        editor.popups.show(
          "kbArticle.insert",
          left - 200,
          top,
          $btn.outerHeight()
        );
        const $selector = $popup.find(".fr-kb-search-results");
        prepareKbArticle($selector);
      } else {
        editor.position.forSelection($popup);
        editor.popups.show("kbArticle.insert");
        const $selector = $popup.find(".fr-kb-search-results");
        prepareKbArticle($selector);
      }
    }
  }

  function prepareKbArticle($selector) {
    if ($selector.children().length > 1) {
      // Already populated (we assume the default + actual options)
      return;
    }
    // Clear existing options
    $selector.empty();

    // Trigger event for parent component to populate tokens
    editor.events.trigger("insertArticleOptions", [$selector]);
  }

  // Initialize the insert popup
  function _initInsertPopup() {
    // Create HTML for KB article selection layer
    const kbSelectionHtml = `
        <div class="fr-kb-article-selection-layer fr-layer fr-active" id="fr-kb-article-selection-layer-${editor.id}">
         <div style="margin-bottom: 10px;">
      <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
        <input type="checkbox" id="fr-kb-embed-checkbox" />
        <span class="slider round"></span>
        <span style="margin-left: 25px;">Embedded</span>
      </label>
    </div>
    <div class="fr-input-line fr-kb-search-container" style="margin-bottom: 10px;">
      <input 
        type="text" 
        class="fr-kb-search" 
        placeholder="Search for Articles" 
        tabindex="1"
        aria-label="Search for Articles"
        style="width: 100%; padding: 6px 10px; border: 1px solid #ccc; border-radius: 4px;"
      />
    </div>
        <div class="fr-kb-search-results" style="height: 200px; border: 1px solid #ccc; overflow-x: auto; overflow-y: auto; background: white;"></div>
    `;

    // Set the template in the popup
    const $popup = editor.popups.create("kbArticle.insert", {
      kb_selection_layer: kbSelectionHtml,
    });

    // Add event handlers for the search
    const $searchInput = $popup.find(".fr-kb-search");
    const $searchButton = $popup.find(".fr-kb-search-btn");
    const $resultsContainer = $popup.find(".fr-kb-search-results");

    $searchInput.on("keydown", function (e) {
      if (e.which === 13) {
        // Enter key
        searchKbArticles($searchInput.val());
        e.preventDefault();
        return false;
      }
    });

    $searchButton.on("click", function () {
      searchKbArticles($searchInput.val());
    });

    function searchKbArticles(query) {
      if (!query || query.length < 3) {
        $resultsContainer.html(
          '<div class="fr-kb-search-message">Please enter at least 3 characters</div>'
        );
        return;
      }

      $resultsContainer.html(
        '<div class="fr-kb-search-message">Searching...</div>'
      );

      // Use the provided httpClient to search for KB articles
      httpClient
        .get("api/kb", {
          sAction: "listing",
          search: query,
          limit: 10,
        })
        .then((response) => {
          if (response && response.kb && response.kb.results) {
            displaySearchResults(response.kb.results);
          } else {
            $resultsContainer.html(
              '<div class="fr-kb-search-message">No results found</div>'
            );
          }
        })
        .catch((error) => {
          console.error("KB search error:", error);
          $resultsContainer.html(
            '<div class="fr-kb-search-message">Error searching KB articles</div>'
          );
        });
    }

    function displaySearchResults(results) {
      if (!results || results.length === 0) {
        $resultsContainer.html(
          '<div class="fr-kb-search-message">No results found</div>'
        );
        return;
      }

      let html = '<div class="fr-kb-results-list">';

      results.forEach((article) => {
        html += `
            <div class="fr-kb-result-item" 
                data-id="${article.id}" 
                data-title="${escapeHtml(article.title)}" 
                data-url="${article.url || "#"}"
                tabindex="2">
            <div class="fr-kb-result-title">${escapeHtml(article.title)}</div>
            <div class="fr-kb-result-summary">${escapeHtml(
              article.summary || ""
            )}</div>
            </div>
        `;
      });

      html += "</div>";
      $resultsContainer.html(html);

      // Add click event to result items
      $resultsContainer
        .find(".fr-kb-result-item")
        .on("click", function () {
          $resultsContainer.find(".fr-kb-result-item").removeClass("selected");
          $(this).addClass("selected");
        })
        .on("keydown", function (e) {
          if (e.which === 13 || e.which === 32) {
            // Enter or Space
            $(this).click();
            e.preventDefault();
          }
        });
    }

    // Handle scroll events
    if (editor.$wp) {
      editor.events.$on(editor.$wp, "scroll.kbArticle-insert", function () {
        if (get() && editor.popups.isVisible("kbArticle.insert")) {
          _showInsertPopup();
        }
      });
    }

    return $popup;
  }

  // Remove KB article block
  function remove() {
    const block = get();

    if (editor.events.trigger("kbArticle.beforeRemove", [block]) === false) {
      return false;
    }

    if (block) {
      editor.selection.save();
      editor.$(block).replaceWith("");
      editor.selection.restore();
      _hideEditPopup();
    }
  }

  // Insert KB article from the UI component data
  function insertKbArticle($selectedItem, editor) {
    if (!$selectedItem || Object.keys($selectedItem).length === 0) {
      alert("Please select a KB article");
      return;
    }

    const kbId = $selectedItem.id;
    const kbTitle = $selectedItem.label;
    const kbUrl = $selectedItem.url;
    const isEmbedded = $selectedItem.isEmbedded;

    if (!kbId) {
      alert("Invalid KB article selection");
      return;
    }

    // Save selection before inserting the KB article
    editor.selection.save();
    
    // Insert the KB article at the current position
    insertKbArticleHtml(
      kbId, 
      kbUrl, 
      kbTitle, 
      "", 
      [], 
      [], 
      "", 
      "", 
      { kbembedded: isEmbedded },
      editor
    );

    // Hide the popup
    editor.popups.hide("kbArticle.insert");
  }

  // Insert KB article HTML
  async function insertKbArticleHtml(
    kbId,
    kbUrl,
    title,
    subTitle,
    teams,
    orgs,
    visibility,
    updated,
    embedded,
    editor
  ) {
    if (!kbId) {
      return;
    }

    // Clean and format values
    kbId = kbId.replace(/[^a-zA-Z0-9-]+/g, "");
    kbUrl = kbUrl || "";
    title = title || "";
    subTitle = subTitle || "";

    // Restore selection before insertion
    editor.selection.restore();

    if (embedded.kbembedded) {
      // For embedded articles, we need to fetch the full content
      try {
        const res = await knowledgeAPI.fetchKnowledgeListingArticle({
          filter: [
            { property: 'id', value: kbId }
          ]
        });

        // Insert at cursor position
        editor.html.insert(
          `<div 
            contenteditable="false"
            data-qtip="${kbUrl}"
            class="bt-block-article collapsable bt-block-substituted fr-deletable"
            data-bt-block="bt-article"
            data-kb-id="${kbId}"
            data-kb-embedded="1"
          >
            <span class="bt-block-title head">
              <span type="article-title">${escapeHtml(title)}</span>
              <span class="spacer">&nbsp;</span>
              <a class="open-article-btn" href="${kbUrl}" target="_blank">
                <i class="pi pi-external-link">&nbsp;</i>
              </a>
              <i class="pi pi-angle-down control">&nbsp;</i>
            </span>
            
            <span class="bt-block-inner content">
              <span class="summary">
                <span>${res.items && res.items[0] ? res.items[0].body : ''}</span>
              </span>
            </span>
          </div>
          <p></p>`
        );

        // Create a new paragraph after the inserted content and move cursor there
        setTimeout(() => {
          const articleNodes = editor.$el.find('div[data-bt-block="bt-article"][data-kb-id="' + kbId + '"]');
          if (articleNodes.length > 0) {
            const $lastArticle = editor.$(articleNodes[articleNodes.length - 1]);
            const $nextEl = $lastArticle.next('p');
            
            if ($nextEl.length) {
              editor.selection.setAtStart($nextEl.get(0));
              editor.selection.restore();
            }
          }
        }, 50);

        editor.events.trigger("contentChanged");
        editor.events.trigger("openInsertKbArticle");
      } catch (error) {
        console.error("Error fetching KB article:", error);
        editor.events.trigger("contentChanged");
      }
    } else {
      // For link-only inserts
      const text = `
        <span class="bt-block-title">
            <span class="bt-title-link" type="article-title">${escapeHtml(
              title
            )}</span>
            <span class="spacer">&nbsp;</span>
        </span>`;

      editor.html.insert(
        `<span contenteditable="false"
            data-qtip="${kbUrl}"
            class="bt-block-article bt-block-link-only fr-deletable"
            data-bt-block="bt-article"
            data-kb-id="${kbId}"
            data-kb-embedded="">
            ${text}
        </span>`
      );

      // Move cursor after the inserted span
      setTimeout(() => {
        const articleNodes = editor.$el.find('span[data-bt-block="bt-article"][data-kb-id="' + kbId + '"]');
        if (articleNodes.length > 0) {
          const $lastArticle = editor.$(articleNodes[articleNodes.length - 1]);
          editor.selection.setAfter($lastArticle.get(0));
          editor.selection.restore();
        }
      }, 50);

      editor.events.trigger("contentChanged");
      editor.events.trigger("openInsertKbArticle");
    }
  }

  // Helper to escape HTML
  function escapeHtml(text) {
    if (!text) return "";

    const map = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#039;",
    };

    return text.replace(/[&<>"']/g, (m) => map[m]);
  }

  // Format org/team chips for display
  function formatChips(items, className) {
    if (!items || !items.length) return "";

    return items
      .map((item) => {
        return `<span class="chip ${className}">${escapeHtml(item)}</span>`;
      })
      .join("");
  }

  // Split selection for proper insertion
  function _split() {
    if (!editor.selection.isCollapsed()) {
      editor.selection.save();
      let markers = editor.$el
        .find(".fr-marker")
        .addClass("fr-unprocessed")
        .toArray();

      while (markers.length) {
        const $marker = editor.$(markers.pop());
        $marker.removeClass("fr-unprocessed");

        // Get deepest parent
        const deepParent = editor.node.deepestParent($marker.get(0));

        if (deepParent) {
          let node = $marker.get(0);
          let closeStr = "";
          let openStr = "";

          do {
            node = node.parentNode;

            if (!editor.node.isBlock(node)) {
              closeStr = closeStr + editor.node.closeTagString(node);
              openStr = editor.node.openTagString(node) + openStr;
            }
          } while (node !== deepParent);

          const markerStr =
            editor.node.openTagString($marker.get(0)) +
            $marker.html() +
            editor.node.closeTagString($marker.get(0));

          $marker.replaceWith('<span id="fr-break"></span>');
          let h = deepParent.outerHTML;

          h = h.replace(
            /<span id="fr-break"><\/span>/g,
            closeStr + markerStr + openStr
          );

          deepParent.outerHTML = h;
        }

        markers = editor.$el.find(".fr-marker.fr-unprocessed").toArray();
      }

      editor.html.cleanEmptyTags();
      editor.selection.restore();
    }
  }

  // Initialize plugin
  function _init() {
    // Edit on keyup
    editor.events.on("keyup", function (e) {
      if (e.which !== FroalaEditor.KEYCODE.ESC) {
        _edit(e);
      }
    });

    editor.events.on("window.mouseup", _edit);

    if (editor.helpers.isMobile()) {
      editor.events.$on(editor.$doc, "selectionchange", _edit);
    }

    // Initialize the insert popup with delayed loading
    _initInsertPopup();

    // Initialize on KB article block
    if (isKbArticleBlock(editor.el)) {
      editor.$el.addClass("fr-view");
    }

    // Handle ESC key in edit popup
    editor.events.on(
      "toolbar.esc",
      function () {
        if (editor.popups.isVisible("kbArticle.edit")) {
          editor.events.disableBlur();
          editor.events.focus();
          return false;
        }
      },
      true
    );

    // Add handler for collapsible KB articles
    editor.events.on("mousedown", function (e) {
      const $target = editor.$(e.target);

      // Toggle collapsible KB articles when clicking on the control icon
      if ($target.hasClass("control") || $target.parent().hasClass("control")) {
        const $article = $target.closest(".bt-block-article");

        if ($article.length) {
          $article.toggleClass("collapsed");
          e.preventDefault();
          e.stopPropagation();
        }
      }
    }, true);
  }

  // Return the public API
  return {
    _init: _init,
    get: get,
    allSelected: allSelected,
    remove: remove,
    insertKbArticle: insertKbArticle,
    showInsertPopup: _showInsertPopup,
  };
};

// Register the button to show KB article insert popup
FroalaEditor.RegisterCommand("insertKbArticle", {
  title: "Insert Article",
  undo: false,
  focus: true,
  refreshOnCallback: false,
  popup: true,
  callback: function () {
    this.events.trigger("openInsertKbArticle", [this]);
  },
  plugin: "kbArticle",
});

// Open article in a new tab
FroalaEditor.RegisterCommand("kbArticleOpen", {
  title: "Open Article",
  undo: false,
  refresh: function ($btn) {
    const block = this.kbArticle.get();
    if (block) {
      $btn.removeClass("fr-hidden");
    } else {
      $btn.addClass("fr-hidden");
    }
  },
  callback: function () {
    const block = this.kbArticle.get();
    let kbId, url;

    if (block) {
      kbId = block.getAttribute("data-kb-id") || null;
      url =
        kbId &&
        block.querySelector("a") &&
        block.querySelector("a").getAttribute("href");

      if (url) {
        window.open(url, "_blank");
      } else {
        console.error("Invalid KB block: missing URL");
      }

      this.popups.hide("kbArticle.edit");
    }
  },
  plugin: "kbArticle",
});

// Remove KB article
FroalaEditor.RegisterCommand("kbArticleRemove", {
  title: "Remove",
  callback: function () {
    this.kbArticle.remove();
  },
  refresh: function ($btn) {
    const block = this.kbArticle.get();
    if (block) {
      $btn.removeClass("fr-hidden");
    } else {
      $btn.addClass("fr-hidden");
    }
  },
  plugin: "kbArticle",
});

export default kbArticle;
