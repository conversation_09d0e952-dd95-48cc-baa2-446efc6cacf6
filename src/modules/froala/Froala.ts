import FroalaEditor from 'froala-editor';
import { Glyphs } from './Glyphs';
import './Icons';
import type { ButtonPermission } from '@/types/froala/ButtonPermission';
import { createFroalaConfig } from './ButtonsPermission';
import { globalEditor } from './EditorRef';
import tabs from './plugins/tab/tab.js';
import divutils from './plugins/divutils/divutils.js';
import accordion from './plugins/accordion/accordion.js';
import kbArticle from './plugins/kbArticle/kbArticle.js';
import fileManager from './plugins/fileManager/fileManager.js';
import token from './plugins/token/token.js';
import { useUserStore } from '@/stores/user';
import { codeEditor } from './plugins/code/Editor.js';
import { useAuthStore } from '@/stores/auth';

// Get the editor instance
// const editor = globalEditor.getInstance();

// Extend FroalaEditor.COMMANDS type
declare module 'froala-editor' {
    interface commandsInterface {
        insertFile: {
            title: string;
        };
        imageAlign: {
            options: object;
            html: string;
        };
    }
}

let ret: any = {
    // Froala Editor has these 4 groups built-in to the toolbar
    moreText: {
        buttons: [],
        align: 'left',
        // For email comms, just collapse all the text buttons into the menu
        buttonsVisible: 0//this.getUseEmailCommsMenu() ? 0 : 3,
    },
    moreParagraph: {
        buttons: [],
        align: 'left',
        buttonsVisible: 3,
    },
    moreRich: {
        buttons: [],
        align: 'left',
        buttonsVisible: 0, // Will be changed later on
    },
    moreMisc: {
        buttons: [],
        align: 'left',
        buttonsVisible: 1,
    },
};

FroalaEditor.RegisterCommand('fixCodeView', {
    title: 'Code View',
    focus: false,
    undo: false,
    refreshAfterCallback: true,
    callback: function() {
    },
});

function setupFixedCodeView() {
    FroalaEditor.RegisterCommand('fixCodeView', {
        title: 'Code View',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        callback: function() {
            const codeBtn = this.$tb.find('.fr-command[data-cmd="fixCodeView"]');

            this.codeView.toggle();
            codeBtn[0].classList.remove('fr-disabled');
        },
    });
}

function setupFixedPDFExport() {
    FroalaEditor.RegisterCommand('exportPDF', {
        title: 'Download PDF',
        focus: false,
        undo: false,
        callback: function() {
            
        },
    });
}

function setupUnIndent() {
    // CN-3536 Froala doesn't like showing the "outdent" button, so we have to make our own
    FroalaEditor.RegisterCommand('unindent', {
        title: 'Decrease Indent (Ctrl+[)',
        focus: false,
        undo: false,
        callback: function() {
        },
    });
}

function setupFigureAlignment() {
    FroalaEditor.DefineIcon('figureAlign', {
        NAME: 'align-justify',
        // template: 'svg',
        // PATH: 'alignJustify'
    });

    FroalaEditor.RegisterCommand('figureAlign', {
        title: 'Align',
        type: 'dropdown',
        options: {
            'left': 'Align Left',
            'center': 'Align Center', 
            'right': 'Align Right'
        },
        html: function() {
        },
        callback: function(cmd, opt) {
            
        },
    });
}

function setupFigureCaption() {
    FroalaEditor.DefineIcon('figureCaption', {
        NAME: 'commenting',
        FA5NAME: 'comment-alt',
        // template: 'svg',
        // PATH: FroalaEditor.SVG.imageCaption,
    });

    FroalaEditor.RegisterCommand('figureCaption', {
        title: 'Caption',
        undo: true,
        focus: false,
        refreshAfterCallback: true,
        callback: function() {
            
        },
    });
}

function setupAccordions() {
    FroalaEditor.PLUGINS.accordion = accordion;
}

function setupTabs(){
    FroalaEditor.PLUGINS.tabs = tabs;
}

function setupDeleteBtn() {
    FroalaEditor.PLUGINS.divutils = divutils;
}

function setupManageFiles() {
    FroalaEditor.PLUGINS.fileManager = fileManager;
}

function setupKbInsert() {
    FroalaEditor.RegisterCommand('kbLinkInsert', {
        title: 'Insert KB Article',
        undo: false,
        focus: false,
        refreshOnCallback: true,
        callback: function() {}
    });
}

function setupPanelInserts() {
    FroalaEditor.RegisterCommand('panel-divs', {
        title: 'Insert Panel',
        type: 'dropdown',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        options: {
            'panel panel-danger': 'Danger panel',
            'panel panel-warning': 'Warning panel',
            'panel panel-info': 'Info panel',
            'panel panel-success': 'Success panel',
            'alert alert-danger': 'Danger alert',
            'alert alert-warning': 'Warning alert',
            'alert alert-info': 'Info alert',
            'alert alert-success': 'Success alert',
        },
        callback: function(cmd, val) {
        }
    });
}

function setupTeamviewer() {
    FroalaEditor.DefineIconTemplate('teamviewer_tpl', '<i class="svg-glyph team-viewer-dark team-viewer-html-btn"></i>');
    FroalaEditor.DefineIcon('teamviewer', {template: 'teamviewer_tpl'});
    FroalaEditor.RegisterCommand('teamviewer', {
        title: 'TeamViewer',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        callback: function() {
        }
    });
}

function setupCannedResponses() {
    FroalaEditor.RegisterCommand('canned_responses', {
        title: 'Quick Replies',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        callback: function() {
        }
    });
}

function setupEmailBtns() {
    FroalaEditor.RegisterCommand('emailAttachFiles', {
        title: 'Attach File(s)',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        callback: function() {
        }
    });

    FroalaEditor.RegisterCommand('emailAttachments', {
        title: 'Attachments',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        callback: function() {
        }
    });
}

function setupInlineImgAlign() {
    FroalaEditor.DefineIcon('inlineImgAlign', {NAME: 'paint-brush'});
    FroalaEditor.RegisterCommand('inlineImgAlign', {
        title: 'Inline Styles',
        type: 'dropdown',
        focus: false,
        undo: false,
        refreshAfterCallback: true,
        options: {
            left: 'Align Left',
            center: 'Align Center',
            right: 'Align Right',
        },
        callback: function(cmd, val) {
  
        },
    });
}

function setupTokenInsert() {
    FroalaEditor.PLUGINS.token = token;
}

function registerCodeBtn() {
    FroalaEditor.PLUGINS.codeEditor = codeEditor;
}

function setupEmbeddedKb() {
    FroalaEditor.PLUGINS.kbArticle = kbArticle;

    // Register command for opening insert article view
    // FroalaEditor.RegisterShortcut(FroalaEditor.KEYCODE.K, 'insertKb', null, 'K');
    // FroalaEditor.RegisterCommand('insertKb', {
    //     title: 'Insert Article',
    //     undo: false,
    //     focus: true,
    //     refreshOnCallback: false,
    //     popup: true,
    //     width: 300,
    //     callback: function() {
    //     },
    //     plugin: 'kb',
    // });

    // // cmd to insert kb into editor
    // FroalaEditor.RegisterCommand('kbInsert', {
    //     focus: false,
    //     refreshAfterCallback: false,
    //     callback: function() {
    //     },
    //     refresh: function($btn) {
    //     },
    //     plugin: 'kb',
    // });
    // // open article edit view in alt
    // FroalaEditor.DefineIcon('kbOpen', {
    //     NAME: 'external-link',
    //     FA5NAME: 'chevron-right',
    // });
    // FroalaEditor.RegisterCommand('kbOpen', {
    //     title: 'Open Article',
    //     undo: false,
    //     refresh: function($btn) {
            
    //     },
    //     callback: function() {
            
    //     },
    //     plugin: 'kb',
    // });

    // FroalaEditor.DefineIcon('kbEdit', {
    //     NAME: 'edit',
    //     FA5NAME: 'pencil',
    // });
    // FroalaEditor.RegisterCommand('kbEdit', {
    //     title: 'Edit Article',
    //     undo: false,
    //     refresh: function($btn) {
            
    //     },
    //     callback: function() {
            
    //     },
    //     plugin: 'kb',
    // });

    // FroalaEditor.DefineIcon('kbRemove', {NAME: 'unlink'});
    // FroalaEditor.RegisterCommand('kbRemove', {
    //     title: 'Remove',
    //     callback: function() {
    //     },
    //     refresh: function($btn) {
    //     },
    //     plugin: 'kb',
    // });
    // FroalaEditor.DefineIcon('imageDisplay', {
    //     template: 'relay_glyphs',
    //     NAME: Glyphs.getIconName('group'),
    // });
}

function setupExtraButtons() {
    if (!FroalaEditor.COMMANDS.insertFile) {
        FroalaEditor.COMMANDS.insertFile = {
            title: 'Insert File'
        };
    }

    setupFixedCodeView();
    setupFixedPDFExport();
    setupUnIndent();
    setupFigureAlignment();
    setupFigureCaption();
    setupAccordions();
    setupTabs();
    setupDeleteBtn();
    setupManageFiles();
    setupKbInsert();
    setupPanelInserts();
    setupInlineImgAlign();
    setupTeamviewer();
    setupCannedResponses();
    setupEmbeddedKb();
    setupTokenInsert();
    registerCodeBtn();
    // setUpEmailBtns();
}

function appendButton(btnGroup: any, btn: any, btnIcon?: any) {
    if (typeof btn === 'string') {
        btn = [
            {
                name: btn,
                icon: btnIcon,
            },
        ];
    }

    // if (Ext.isArray(btn)) {
    btn.forEach((b: any) => {
        if (typeof b === 'string') {
            b = {name: b};
        }

        // Froala has built-in SVG icons, let's use them if we can
        if (b.icon === 'svg') {
            FroalaEditor.DefineIcon(b.name, {
                template: 'svg',
                PATH: FroalaEditor.SVG[b.name],
            });
        }
        else {
            // Icons are generally "editor-XXX-16", so let's default to that
            if (!b.icon) {
                b.icon =
                    'editor-' +
                    b.name
                        .split(/(?=[A-Z])/)
                        .join('-')
                        .toLowerCase() +
                    '-16';
            }

            FroalaEditor.DefineIcon(b.name, {
                template: 'relay_glyphs',
                NAME: Glyphs.getIconName(b.icon),
            });
        }

        ret[btnGroup].buttons.push(b.name);
    });
    // }
}


const parseButtons = (config: any, options: Partial<ButtonPermission> = {}) => {
    const froalaConfig = createFroalaConfig(options);
    setupExtraButtons();
    ret = {
        moreText: {
            buttons: [],
            align: 'left',
            buttonsVisible: 3
        },
        moreParagraph: {
            buttons: [],
            align: 'left',
            buttonsVisible: 3,
        },
        moreRich: {
            buttons: [],
            align: 'left',
            buttonsVisible: 0,
        },
        moreMisc: {
            buttons: [],
            align: 'left',
            buttonsVisible: 1,
        },
    }

    if (froalaConfig.enableParagraphFormat) {
        appendButton('moreText', 'paragraphFormat', 'editor-text-16');
    }
    if (froalaConfig.enableBasicTextStyle) {
        appendButton('moreText', ['bold', 'italic', 'underline'], undefined);
    }
    if (froalaConfig.enableColors) {
        appendButton('moreText', ['textColor', 'backgroundColor'], undefined);
    }
    if (froalaConfig.enableAdvancedTextStyle) {
        appendButton('moreText', [{name: 'strikeThrough', icon: 'editor-strikethrough-16'}, 'subscript', 'superscript'], undefined);
    }
    if (froalaConfig.enableFontStyles) {
        ret.moreText.buttons.push('fontSize');
    }
    if (froalaConfig.enableLineHeight) {
        ret.moreText.buttons.push('lineHeight');
    }

    if (froalaConfig.enableParagraphAlign) {
        appendButton('moreParagraph', 'align', 'editor-align-left-16');
        FroalaEditor.DefineIcon('align-left', {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('editor-align-left-16'),
        });
        FroalaEditor.DefineIcon('align-right', {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('editor-align-right-16'),
        });
        FroalaEditor.DefineIcon('align-center', {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('editor-align-center-16'),
        });
        FroalaEditor.DefineIcon('align-justify', {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('editor-align-justify-16'),
        });
    }

    if (froalaConfig.enableLists) {
        appendButton('moreParagraph', [
            {name: 'formatUL', icon: 'editor-bullet-16'},
            {name: 'formatOL', icon: 'editor-numbers-16'},
        ]);
    }

    if (froalaConfig.enableAdvanced) {
        appendButton('moreParagraph', [
            {name: 'unindent', icon: 'editor-indent-decrease-16'},
            {name: 'indent', icon: 'editor-indent-increase-16'},
        ]);
    }

    if (froalaConfig.enableParagraphStyle) {
        appendButton('moreParagraph', 'paragraphStyle', 'editor-style-16');
    }

    if (froalaConfig.enableAdvanced) {
        appendButton('moreParagraph', 'quote');
    }

    if (froalaConfig.enableLinks) {
        appendButton('moreRich', 'insertLink', 'editor-link-16');
    }

    if (froalaConfig.enableImage) {
        appendButton('moreRich', 'insertImage', 'editor-image-16');
    }

    if (froalaConfig.enableVideo) {
        appendButton('moreRich', 'insertVideo', 'editor-video-16');
    }

    if (froalaConfig.enableKbInsert) {
        appendButton('moreRich', 'kbLinkInsert', 'editor-article-16');
    }

    if (froalaConfig.enableKbInsertBlock) {
        appendButton('moreRich', 'insertKbArticle', 'editor-article-16');
    }

    if (froalaConfig.enableTokenInsert) {
        appendButton('moreRich', 'insertToken', 'editor-token-16');
    }

    if (froalaConfig.enableUpload) {
        appendButton('moreRich', 'manageFiles', 'folder-open');
    }

    appendButton('moreRich', 'insertCodeBtn', 'x-fa fa-laptop-code');

    if (froalaConfig.enableTeamviewer) {
        ret.moreRich.buttons.push('teamviewer');
    }

    ret.moreRich.buttonsVisible = ret.moreRich.buttons.length;

    if (froalaConfig.enableDivPanels) {
        FroalaEditor.DefineIcon('panel-divs', {
            NAME: 'window',
        });
        ret.moreRich.buttons.push('panel-divs');
    }

    if (froalaConfig.enableTables) {
        appendButton('moreRich', 'insertTable', 'edit-tables-16');
    }

    if (froalaConfig.enableTabs) {
        appendButton('moreRich', 'insertTabs', 'editor-tabs-16');
    }

    if (froalaConfig.enableAccordions) {
        appendButton('moreRich', 'insertAccordion', 'editor-accordion-32');
    }

    if (froalaConfig.enableHR) {
        appendButton('moreRich', 'insertHR', 'editor-line-32');
    }

    if (froalaConfig.enableTrackChanges) {
        ret.moreMisc.buttonsVisible++;
        appendButton('moreMisc', 'trackChanges', 'svg');
    }

    if (froalaConfig.enableSourceEdit) {
        appendButton('moreMisc', [
            {name: 'fixCodeView', icon: 'editor-code-16'},
            {name: 'fullscreen', icon: 'editor-full-screen-16'},
            {name: 'exportPDF', icon: 'editor-download-pdf-16'},
        ]);

        FroalaEditor.DefineIcon('compress', {
            template: 'relay_glyphs',
            NAME: Glyphs.getIconName('compress'),
        });
    }

    if (froalaConfig.enableFontAwesome) {
        appendButton('moreMisc', [
            {name: 'fontAwesome', icon: 'editor-emoticons-32'},
            {name: 'emoticons', icon: 'editor-icons-16'},
            'specialCharacters',
        ]);
    }

    if (froalaConfig.enableSelectAll) {
        appendButton('moreMisc', 'selectAll');
    }

    if (froalaConfig.enableClearFormatting) {
        appendButton('moreMisc', 'clearFormatting', 'editor-clear-all-32');
    }

    if (froalaConfig.enableUndo) {
        appendButton('moreMisc', ['undo', 'redo']);
    }

    if (froalaConfig.enableHelp) {
        appendButton('moreMisc', 'help');
    }

    config.toolbarButtons = ret;
    config.enter = froalaConfig.enableBrEnter ? FroalaEditor.ENTER_BR : FroalaEditor.ENTER_P;
    
    // Set the editor key
    const authStore = useAuthStore();
    config.key = authStore?.editorKey || '';

    return config;
}

export { parseButtons };
