<script setup lang="ts">
import { ref, defineEmits, computed, watch, onMounted } from 'vue';
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue';
import BravoTitlePage from '@services/ui-component-library/components/BravoTypography/BravoTitlePage.vue';
import Bravo<PERSON>conField from '@services/ui-component-library/components/BravoIconField.vue';
import BravoInputIcon from '@services/ui-component-library/components/BravoInputIcon.vue';
import BravoInputText from '@services/ui-component-library/components/BravoInputText.vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const menu = ref();
const activeMenuItem = ref('home'); // Default selected item
const searchQuery = ref('');

const emit = defineEmits(['change-section']);

// Initialize active menu item based on route
onMounted(() => {
  if (route.params.section) {
    activeMenuItem.value = route.params.section as string;
    updateMenuItemClasses();
  }
});

// Watch for route changes to update active menu item
watch(
  () => route.params.section,
  (newSection) => {
    if (newSection) {
      activeMenuItem.value = newSection as string;
      updateMenuItemClasses();
    }
  }
);

// Home item (standalone)
const homeItem = {
  label: 'Home',
  icon: 'pi pi-home',
  command: () => selectMenuItem('home'),
  class: activeMenuItem.value === 'home' ? 'active-menu-item' : ''
};

// Define the menu items
const organizationItems = [
  {
    label: 'General',
    icon: 'pi pi-cog',
    command: () => selectMenuItem('general'),
    class: activeMenuItem.value === 'general' ? 'active-menu-item' : ''
  },
  {
    label: 'Users',
    icon: 'pi pi-user',
    command: () => selectMenuItem('users'),
    class: activeMenuItem.value === 'users' ? 'active-menu-item' : ''
  },
  {
    label: 'Teams',
    icon: 'pi pi-users',
    command: () => selectMenuItem('teams'),
    class: activeMenuItem.value === 'teams' ? 'active-menu-item' : ''
  },
  {
    label: 'Roles & Permissions',
    icon: 'pi pi-lock',
    command: () => selectMenuItem('roles-permissions'),
    class: activeMenuItem.value === 'roles-permissions' ? 'active-menu-item' : ''
  },
  {
    label: 'Security',
    icon: 'pi pi-shield',
    command: () => selectMenuItem('security'),
    class: activeMenuItem.value === 'security' ? 'active-menu-item' : ''
  }
];

// Add CX Manager items
const cxManagerItems = [
  {
    label: 'Workflows',
    icon: 'pi pi-sitemap',
    command: () => selectMenuItem('workflows'),
    class: activeMenuItem.value === 'workflows' ? 'active-menu-item' : ''
  },
  {
    label: 'Fields',
    icon: 'pi pi-list',
    command: () => selectMenuItem('custom-fields'),
    class: activeMenuItem.value === 'custom-fields' ? 'active-menu-item' : ''
  },
  {
    label: 'Layouts',
    icon: 'pi pi-table',
    command: () => selectMenuItem('layouts'),
    class: activeMenuItem.value === 'layouts' ? 'active-menu-item' : ''
  },
  {
    label: 'Tasks',
    icon: 'pi pi-check-square',
    command: () => selectMenuItem('task-templates'),
    class: activeMenuItem.value === 'task-templates' ? 'active-menu-item' : ''
  },
  {
    label: 'Forms',
    icon: 'pi pi-file-edit',
    command: () => selectMenuItem('forms'),
    class: activeMenuItem.value === 'forms' ? 'active-menu-item' : ''
  },
  {
    label: 'Automations',
    icon: 'pi pi-cog',
    command: () => selectMenuItem('automations'),
    class: activeMenuItem.value === 'automations' ? 'active-menu-item' : ''
  }
];

// Add Communications items
const communicationItems = [
  {
    label: 'Channels',
    icon: 'pi pi-comments',
    command: () => selectMenuItem('channels'),
    class: activeMenuItem.value === 'channels' ? 'active-menu-item' : ''
  },
  {
    label: 'Email Configuration',
    icon: 'pi pi-envelope',
    command: () => selectMenuItem('email-configuration'),
    class: activeMenuItem.value === 'email-configuration' ? 'active-menu-item' : ''
  },
  {
    label: 'Quick Replies',
    icon: 'pi pi-reply',
    command: () => selectMenuItem('quick-replies'),
    class: activeMenuItem.value === 'quick-replies' ? 'active-menu-item' : ''
  },
  {
    label: 'Templates',
    icon: 'pi pi-file',
    command: () => selectMenuItem('templates'),
    class: activeMenuItem.value === 'templates' ? 'active-menu-item' : ''
  }
];

// Add Knowledge items
const knowledgeItems = [
  {
    label: 'Libraries',
    icon: 'pi pi-book',
    command: () => selectMenuItem('libraries'),
    class: activeMenuItem.value === 'libraries' ? 'active-menu-item' : ''
  },
  {
    label: 'Tokens',
    icon: 'pi pi-key',
    command: () => selectMenuItem('tokens'),
    class: activeMenuItem.value === 'tokens' ? 'active-menu-item' : ''
  }
];

// Add Connectors items
const connectorsItems = [
  {
    label: 'Marketplace',
    icon: 'pi pi-shopping-cart',
    command: () => selectMenuItem('marketplace'),
    class: activeMenuItem.value === 'marketplace' ? 'active-menu-item' : ''
  },
  {
    label: 'APIs',
    icon: 'pi pi-server',
    command: () => selectMenuItem('apis'),
    class: activeMenuItem.value === 'apis' ? 'active-menu-item' : ''
  }
];

// Function to handle menu item selection
const selectMenuItem = (menuId: string) => {
  activeMenuItem.value = menuId;
  updateMenuItemClasses();
  emit('change-section', menuId);
};

// Update class for each menu item based on active state
const updateMenuItemClasses = () => {
  // Update home item
  homeItem.class = activeMenuItem.value === 'home' ? 'active-menu-item' : '';
  
  organizationItems.forEach(item => {
    const match = item.command.toString().match(/selectMenuItem\('(.+?)'\)/);
    const id = match && match[1] ? match[1] : '';
    item.class = activeMenuItem.value === id ? 'active-menu-item' : '';
  });
  
  cxManagerItems.forEach(item => {
    const match = item.command.toString().match(/selectMenuItem\('(.+?)'\)/);
    const id = match && match[1] ? match[1] : '';
    item.class = activeMenuItem.value === id ? 'active-menu-item' : '';
  });
  
  // Add update for communication items
  communicationItems.forEach(item => {
    const match = item.command.toString().match(/selectMenuItem\('(.+?)'\)/);
    const id = match && match[1] ? match[1] : '';
    item.class = activeMenuItem.value === id ? 'active-menu-item' : '';
  });
  
  // Add update for knowledge items
  knowledgeItems.forEach(item => {
    const match = item.command.toString().match(/selectMenuItem\('(.+?)'\)/);
    const id = match && match[1] ? match[1] : '';
    item.class = activeMenuItem.value === id ? 'active-menu-item' : '';
  });
  
  // Add update for connectors items
  connectorsItems.forEach(item => {
    const match = item.command.toString().match(/selectMenuItem\('(.+?)'\)/);
    const id = match && match[1] ? match[1] : '';
    item.class = activeMenuItem.value === id ? 'active-menu-item' : '';
  });
};

// Filter menu items based on search query
const filteredItems = computed(() => {
  if (!searchQuery.value.trim()) {
    return [
      homeItem,
      {
        separator: true
      },
      {
        label: 'Organization',
        items: organizationItems
      },
      {
        label: 'CX Manager',
        items: cxManagerItems
      },
      {
        label: 'Communications',
        items: communicationItems
      },
      {
        label: 'Knowledge',
        items: knowledgeItems
      },
      {
        label: 'Connectors',
        items: connectorsItems
      }
    ];
  }

  const query = searchQuery.value.toLowerCase().trim();
  
  // Check if home item matches search
  const homeMatches = homeItem.label.toLowerCase().includes(query);
  
  // Filter other section items
  const filteredOrgItems = organizationItems.filter(item => 
    item.label.toLowerCase().includes(query)
  );
  
  // Filter CX Manager items
  const filteredCXItems = cxManagerItems.filter(item => 
    item.label.toLowerCase().includes(query)
  );
  
  // Filter Communication items
  const filteredCommItems = communicationItems.filter(item => 
    item.label.toLowerCase().includes(query)
  );
  
  // Filter Knowledge items
  const filteredKnowledgeItems = knowledgeItems.filter(item => 
    item.label.toLowerCase().includes(query)
  );
  
  // Filter Connectors items
  const filteredConnectorsItems = connectorsItems.filter(item => 
    item.label.toLowerCase().includes(query)
  );
  
  // Return filtered items
  const result = [];
  
  // Add home if it matches
  if (homeMatches) {
    result.push(homeItem);
    
    // Add separator if there are other matches
    if (filteredOrgItems.length > 0 || filteredCXItems.length > 0 || 
        filteredCommItems.length > 0 || filteredKnowledgeItems.length > 0 || 
        filteredConnectorsItems.length > 0) {
      result.push({
        separator: true
      });
    }
  }
  
  // Add other filtered sections
  if (filteredOrgItems.length > 0) {
    result.push({
      label: 'Organization',
      items: filteredOrgItems
    });
  }
  
  if (filteredCXItems.length > 0) {
    result.push({
      label: 'CX Manager',
      items: filteredCXItems
    });
  }
  
  if (filteredCommItems.length > 0) {
    result.push({
      label: 'Communications',
      items: filteredCommItems
    });
  }
  
  if (filteredKnowledgeItems.length > 0) {
    result.push({
      label: 'Knowledge',
      items: filteredKnowledgeItems
    });
  }
  
  if (filteredConnectorsItems.length > 0) {
    result.push({
      label: 'Connectors',
      items: filteredConnectorsItems
    });
  }
  
  return result;
});

// Clear search query
const clearSearch = () => {
  if (searchQuery.value) {
    searchQuery.value = '';
  }
};

// Original items for reference
const items = ref([
  homeItem,
  {
    separator: true
  },
  {
    label: 'Organization',
    items: organizationItems
  },
  {
    label: 'CX Manager',
    items: cxManagerItems
  },
  {
    label: 'Communications',
    items: communicationItems
  },
  {
    label: 'Knowledge',
    items: knowledgeItems
  },
  {
    label: 'Connectors',
    items: connectorsItems
  }
]);

defineExpose({
  toggle: (event: Event) => menu.value?.toggle(event),
  selectMenuItem
});
</script>

<template>
  <div class="settings-sidebar">
    <div class="panel-header">
      <div class="header-content">
        <BravoTitlePage>Settings</BravoTitlePage>
      </div>
    </div>
    <!-- Search container outside of panel-content -->
    <div class="search-container">
      <BravoIconField>
        <BravoInputIcon class="pi pi-search" />
        <BravoInputText 
          v-model="searchQuery" 
          placeholder="Search settings..." 
        />
      </BravoIconField>
    </div>
    <!-- Scrollable panel content -->
    <div class="panel-content">
      <BravoMenu ref="menu" :model="filteredItems" class="w-full" />
      <div v-if="searchQuery && filteredItems.length === 0" class="no-results">
        No matching settings found
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.settings-sidebar {
  min-width: 250px;
  height: 100vh;
  background-color: var(--surface-50);
  border-right: 1px solid var(--surface-border);
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
}

/* Search container styling */
.search-container {
  padding: 0 1rem 0.75rem 1rem;
  max-width: 100%;
  box-sizing: border-box;
  
  :deep(.p-inputtext) {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  
  :deep(.p-input-icon-left) {
    width: 100%;
    max-width: 100%;
  }
}

.panel-content {
  padding: 0rem 1rem 1rem 1rem;
  flex: 1;
  overflow-y: auto;
  
  :deep(.p-menu) {
    border: none;
    width: 100%;
    background-color: var(--surface-50);
  }
  
  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 8px;
    background-color: var(--surface-50);
  }
  
  &::-webkit-scrollbar:hover {
    background-color: var(--surface-100);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--surface-300);
    border-radius: 8px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--surface-400);
  }
}

:deep(.pi-times, .pi-search) {
  cursor: pointer;
}

.no-results {
  text-align: center;
  padding: 2rem 0;
  color: var(--text-color-secondary);
  font-style: italic;
}

:deep(.active-menu-item) {
  background-color: var(--surface-200) !important;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
}

:deep(.p-menu-separator) {
  margin: 0.5rem 0;
}
</style>
