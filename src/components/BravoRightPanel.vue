<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Accordion from 'primevue/accordion'
import AccordionTab from 'primevue/accordiontab'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoEventsList from './BravoEventsList.vue'
import OtherEvents from './OtherEvents.vue'
import type { Issue } from '../services/IssuesAPI'

interface PanelConfig {
  id: string
  title: string
  component: any // Consider creating a more specific type
  visible: boolean
  expanded: boolean
  order: number
  props?: Record<string, any>
}

const props = defineProps<{
  issue: Issue
}>()

// Available panels that can be added
const availablePanels = ref<PanelConfig[]>([
  {
    id: 'otherEvents',
    title: 'Other Events',
    component: OtherEvents,
    visible: true,
    expanded: false,
    order: 0,
    props: {
      issueId: props.issue.id
    }
  }
])

// Track if we have a new panel available
const hasNewPanelAvailable = ref(false)

// Active panels
const panels = ref<PanelConfig[]>([
  {
    id: 'caseInfo',
    title: 'Case Information',
    component: 'div',
    visible: true,
    expanded: true,
    order: 0,
    props: {
      class: 'case-info-content'
    }
  },
  {
    id: 'events',
    title: 'Interaction Events',
    component: BravoEventsList,
    visible: true,
    expanded: false,
    order: 1,
    props: {
      issueId: props.issue.id
    }
  }
])

// Watch for changes in available panels
watch(availablePanels, (newPanels) => {
  if (newPanels.length > 0) {
    hasNewPanelAvailable.value = true
  }
}, { deep: true })

// Function to add a new panel from available panels
const addPanel = (panel: PanelConfig) => {
  // Add to active panels with next order
  const nextOrder = Math.max(...panels.value.map(p => p.order), -1) + 1
  panels.value.push({
    ...panel,
    order: nextOrder,
    expanded: false
  })
  
  // Remove from available panels
  availablePanels.value = availablePanels.value.filter(p => p.id !== panel.id)
  
  // Reset new panel flag if no more available panels
  if (availablePanels.value.length === 0) {
    hasNewPanelAvailable.value = false
  }
}

// Set initial state of hasNewPanelAvailable
onMounted(() => {
  hasNewPanelAvailable.value = availablePanels.value.length > 0
})

// Example function to simulate adding a new available panel
const addAvailablePanel = (panel: PanelConfig) => {
  availablePanels.value.push(panel)
}

// Toggle panel expansion
const togglePanel = (panelId: string) => {
  const panel = panels.value.find(p => p.id === panelId)
  if (panel) {
    panel.expanded = !panel.expanded
  }
}

// Drag and drop functionality
const draggedPanel = ref<PanelConfig | null>(null)

const onDragStart = (panel: PanelConfig, event: DragEvent) => {
  if (!event.dataTransfer) return
  
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/plain', panel.id) // Required for Firefox
  draggedPanel.value = panel
  
  // Add dragging class to the correct element
  const blockElement = (event.target as HTMLElement).closest('.collapsible-block')
  if (blockElement) {
    blockElement.classList.add('dragging')
  }
}

const onDragEnd = (event: DragEvent) => {
  const blocks = document.querySelectorAll('.collapsible-block')
  blocks.forEach(block => {
    block.classList.remove('dragging', 'drag-over')
  })
  draggedPanel.value = null
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  const blockElement = (event.target as HTMLElement).closest('.collapsible-block')
  if (!blockElement || blockElement.classList.contains('dragging')) return
  
  blockElement.classList.add('drag-over')
}

const onDragLeave = (event: DragEvent) => {
  const blockElement = (event.target as HTMLElement).closest('.collapsible-block')
  if (blockElement) {
    blockElement.classList.remove('drag-over')
  }
}

const onDrop = (targetPanel: PanelConfig, event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  
  const blocks = document.querySelectorAll('.collapsible-block')
  blocks.forEach(block => {
    block.classList.remove('dragging', 'drag-over')
  })
  
  if (!draggedPanel.value || draggedPanel.value.id === targetPanel.id) {
    draggedPanel.value = null
    return
  }
  
  // Get the current orders
  const draggedOrder = draggedPanel.value.order
  const targetOrder = targetPanel.order
  
  // Update orders
  panels.value = panels.value.map(panel => {
    if (panel.id === draggedPanel.value?.id) {
      return { ...panel, order: targetOrder }
    }
    if (panel.id === targetPanel.id) {
      return { ...panel, order: draggedOrder }
    }
    return panel
  }).sort((a, b) => a.order - b.order)
  
  draggedPanel.value = null
}

// Current active tab (0 = Details, 1 = Copilot)
const activeTab = ref(0)
</script>

<template>
  <div class="right-panel">
    <TabView v-model:activeIndex="activeTab" class="right-panel-tabs">
      <TabPanel header="Details" value="details">
        <div class="panel-content">
          <!-- New Panel Alert -->
          <div v-if="hasNewPanelAvailable" class="new-panel-alert">
            <div class="alert-content">
              <i class="pi pi-info-circle" />
              <span>New detail blocks available!</span>
            </div>
            <div class="available-panels">
              <div 
                v-for="panel in availablePanels" 
                :key="panel.id"
                class="available-panel"
              >
                <span>{{ panel.title }}</span>
                <BravoButton
                  icon="pi pi-plus"
                  severity="secondary"
                  text
                  @click="addPanel(panel)"
                />
              </div>
            </div>
          </div>

          <!-- Existing Panels -->
          <TransitionGroup name="panel-list">
            <div 
              v-for="panel in panels"
              :key="panel.id"
              v-show="panel.visible"
              class="collapsible-block"
              draggable="true"
              @dragstart="onDragStart(panel, $event)"
              @dragend="onDragEnd($event)"
              @dragover="onDragOver($event)"
              @dragleave="onDragLeave($event)"
              @drop="onDrop(panel, $event)"
            >
              <div class="block-header">
                <div class="drag-handle">
                  <i class="pi pi-bars" />
                </div>
                <h3>{{ panel.title }}</h3>
                <BravoButton 
                  :icon="panel.expanded ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
                  class="collapse-toggle-btn" 
                  severity="secondary"
                  text
                  @click.stop="togglePanel(panel.id)"
                />
              </div>
              
              <div v-if="panel.expanded" class="block-content">
                <!-- Case Information content -->
                <template v-if="panel.id === 'caseInfo'">
                  <div class="case-field">
                    <strong>ID:</strong> {{ issue.id }}
                  </div>
                  <div v-if="issue.source_data" class="case-field">
                    <strong>Source:</strong> {{ issue.source_data.source }}
                  </div>
                  <div class="case-field">
                    <strong>Available Communications:</strong> 
                    {{ issue.availableComm ? issue.availableComm.length : 0 }}
                  </div>
                </template>
                
                <!-- Dynamic component rendering -->
                <component
                  v-else
                  :is="panel.component"
                  v-bind="panel.props"
                />
              </div>
            </div>
          </TransitionGroup>
        </div>
      </TabPanel>
      
      <TabPanel header="Copilot" value="copilot">
        <div class="panel-content">
          <div class="copilot-placeholder">
            <p>Copilot functionality coming soon...</p>
          </div>
        </div>
      </TabPanel>
    </TabView>
  </div>
</template>

<style scoped>
.right-panel {
  width: 350px;
  border-left: 1px solid var(--surface-200);
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.right-panel-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 150px); /* Account for header and tabs */
}

.collapsible-block {
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  background: white;
  overflow: hidden;
  margin-bottom: 1rem;
  flex-shrink: 0; /* Prevent shrinking when content is large */
}

.collapsible-block:last-child {
  margin-bottom: 0;
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--surface-50);
  border-bottom: 1px solid var(--surface-200);
}

.block-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--surface-900);
}

.collapse-toggle-btn {
  width: 2rem;
  height: 2rem;
}

.block-content {
  padding: 1rem;
}

.events-content {
  max-height: 500px;
  overflow-y: auto;
}

.case-field {
  margin-bottom: 0.75rem;
}

.case-field:last-child {
  margin-bottom: 0;
}

.copilot-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--surface-50);
  border-radius: 6px;
  padding: 2rem;
  color: var(--surface-600);
  font-style: italic;
}

/* Target the TabView and TabPanel to ensure full height */
:deep(.p-tabview) {
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.p-tabview-panels) {
  flex: 1;
  overflow: hidden;
}

:deep(.p-tabview-panel) {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* PrimeVue TabView customizations */
:deep(.p-tabview-nav) {
  border-bottom-color: var(--surface-200);
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
}

:deep(.p-tabview-selected) {
  font-weight: 600;
}

:deep(.p-tabview-panels) {
  height: calc(100% - 50px); /* Subtract tab height */
  padding: 0 !important; /* Remove default padding */
}

.drag-handle {
  cursor: grab;
  padding: 0.5rem;
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  color: var(--surface-600);
}

.drag-handle:active {
  cursor: grabbing;
}

.collapsible-block {
  position: relative;
  transition: transform 0.2s ease, opacity 0.2s ease;
  user-select: none;
  touch-action: none;
}

.collapsible-block.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  cursor: grabbing;
  z-index: 1;
}

.collapsible-block.drag-over {
  border: 2px dashed var(--primary-color);
}

/* Animation transitions */
.panel-list-move {
  transition: transform 0.3s ease;
}

.panel-list-enter-active,
.panel-list-leave-active {
  transition: all 0.3s ease;
}

.panel-list-enter-from,
.panel-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.new-panel-alert {
  background: var(--primary-50);
  border: 1px solid var(--primary-200);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-700);
  margin-bottom: 0.5rem;
}

.alert-content i {
  font-size: 1.2rem;
}

.available-panels {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.available-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid var(--primary-100);
}

.available-panel span {
  color: var(--surface-700);
  font-size: 0.9rem;
}
</style> 