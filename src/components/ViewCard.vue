<script setup lang="ts">
import { ref, onMounted, computed, watchEffect } from 'vue'
import { useCasesStore } from '../stores/cases'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import type { Issue } from '../services/IssuesAPI'
import type { SidebarItem } from '../features/inbox/components/BravoInboxSidebar.vue'

// Define props
interface Props {
  selectedView: SidebarItem | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'select-issue', issue: Issue): void
}>()

const casesStore = useCasesStore()
const cases = ref<Issue[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

// Check if we have a view selected
const hasSelectedView = computed(() => !!props.selectedView)

// Find the view details in the store based on selectedView ID
const viewDetails = computed(() => {
  if (!props.selectedView) return null
  
  // Cast the store to access views
  const storeWithViews = casesStore as any
  const views = storeWithViews.views || []
  
  // Find the view with matching ID
  return views.find((view: any) => view.id === props.selectedView?.id)
})

// Handle case selection
const selectCase = (issue: Issue) => {
  emit('select-issue', issue)
}

// Fetch cases for the selected view
const fetchCasesForView = async () => {
  if (!viewDetails.value) return
  
  isLoading.value = true
  error.value = null
  
  try {
    // Get sort and filter params from the view if they exist
    const sortParams = viewDetails.value.sort || []
    const filterParams = viewDetails.value.filters || []
    
    // Create the params object based on available properties
    const params: any = {}
    
    // Only add sort if it exists
    if (sortParams.length > 0) {
      params.sort = []
      for (const sortParam of sortParams) {
        const [property, direction] = sortParam.split('_')
        params.sort.push({ property: property, direction: direction })
      }
    }
    if (filterParams.length > 0) {
      params.filter = []
      for (const filterParam of filterParams) {
        console.log('filterParam', filterParam);
        params.filter.push({ property: filterParam.filter_field, value: filterParam.filter_compare_field, operator: filterParam.filter_operator })
      }
      console.log('params', params);
   }
    
    // Using any to bypass TypeScript strict checking since we know our API supports these parameters
    // In a production environment, we would properly type this or update the FetchCasesParams interface
    await casesStore.fetchCases(params as any)
    
    // Access the cases from the store
    cases.value = (casesStore as any).issues || []
  } catch (err) {
    console.error('Error fetching cases for view:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load cases'
  } finally {
    isLoading.value = false
  }
}

// Watch for changes to the selected view
watchEffect(() => {
  if (hasSelectedView.value && viewDetails.value) {
    fetchCasesForView()
  }
})
</script>

<template>
  <div class="view-card">
    <div v-if="!hasSelectedView" class="empty-state">
      <p>Select a view from the sidebar to see cases</p>
    </div>
    
    <div v-else class="view-content">
      <div class="view-header">
        <h2>{{ selectedView?.label }}</h2>
        <div class="view-actions">
          <BravoButton 
            icon="pi pi-refresh" 
            size="small" 
            @click="fetchCasesForView"
            :disabled="isLoading"
            aria-label="Refresh View"
            tooltip="Refresh"
          />
        </div>
      </div>
      
      <div v-if="isLoading" class="loading-state">
        <BravoProgressSpinner />
        <span>Loading cases...</span>
      </div>
      
      <div v-else-if="error" class="error-state">
        <p>{{ error }}</p>
        <BravoButton 
          label="Retry" 
          size="small" 
          @click="fetchCasesForView"
        />
      </div>
      
      <div v-else-if="cases.length === 0" class="no-cases">
        <p>No cases found for this view</p>
      </div>
      
      <ul v-else class="cases-list">
        <li 
          v-for="item in cases" 
          :key="item.id" 
          class="case-item"
          @click="selectCase(item)"
        >
          <div class="case-header">
            <h3 class="case-id">{{ item.display_name || item.id }}</h3>
          </div>
          <div class="case-details">
            <div v-if="item.source_data" class="case-source">
              <span class="label">Source:</span> 
              <span class="value">{{ item.source_data.source }}</span>
            </div>
            <div class="case-communications">
              <span class="label">Communications:</span> 
              <span class="value">{{ item.availableComm ? item.availableComm.length : 0 }}</span>
            </div>
          </div>
          <div class="case-actions">
            <BravoButton
              label="View Details"
              size="small"
              @click.stop="selectCase(item)"
              icon="pi pi-arrow-right"
              iconPos="right"
            />
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.view-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--surface-600);
  font-style: italic;
  padding: 2rem;
}

.view-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--surface-200);
}

.view-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--surface-900);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  flex: 1;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  background-color: var(--red-50);
  color: var(--red-600);
  margin: 1rem;
  border-radius: 4px;
}

.no-cases {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--surface-600);
  flex: 1;
}

.cases-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.case-item {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-100);
  transition: background-color 0.2s;
  cursor: pointer;
}

.case-item:hover {
  background-color: var(--surface-50);
}

.case-header {
  margin-bottom: 0.5rem;
}

.case-id {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}

.case-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
}

.label {
  font-weight: 500;
  color: var(--surface-700);
}

.value {
  color: var(--surface-900);
}

.case-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
</style> 