<script setup lang="ts">
import Menu from 'primevue/menu';
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';

const userStore = useUserStore();

const userData = computed(() => ({
    name: userStore.userData?.full_name,
    email: userStore.userData?.email,
    avatar: userStore.userData?.image,
}));


const emit = defineEmits(['logout'])
const router = useRouter()
const menu = ref()

// Function to open AnnounceKit
function openAnnounceKit() {
  // First hide the menu
  if (menu.value) {
    menu.value.hide();
  }
  
  // Try to open the widget via the global AnnounceKit object
  if (window.announcekit && window.announcekit.widget$product_updates) {
    window.announcekit.widget$product_updates.open();
  } else {
    // Fallback to just opening the URL in a new tab
    window.open('https://changelog.ovationcxm.com/widgets/v2/3pgeBy', '_blank');
  }
}

// Function to open Live Chat
function openLiveChat() {
  // First hide the menu
  if (menu.value) {
    menu.value.hide();
  }
  
  // Call the OvationMessenger.show() function
  if (window.OvationMessenger && typeof window.OvationMessenger.show === 'function') {
    window.OvationMessenger.show();
  } else {
    console.error('OvationMessenger is not available');
  }
}

// Define menu items for profile menu
const profileMenuItems = ref([
  {
    items: [
      { label: 'Profile', icon: 'pi pi-user', command: () => router.push('/profile') },
      { label: 'Live Chat', icon: 'pi pi-comments', command: () => openLiveChat() },
      { label: 'Help Site', icon: 'pi pi-question-circle', command: () => window.open('https://help.goboomtown.com/', '_blank') },
      { label: 'Submit Feedback', icon: 'pi pi-comment', command: () => window.open('https://www.ovationcxm.com/ideas', '_blank') },
      { 
        label: 'Product Updates', 
        icon: 'pi pi-gift',
        command: () => openAnnounceKit()
      },
      { label: 'Status Page', icon: 'pi pi-wifi', command: () => window.open('https://status.goboomtown.com/', '_blank') },
      { label: 'Sign Out', icon: 'pi pi-sign-out', command: () => emit('logout') },
    ]
  }
])

// Toggle menu visibility
const toggle = (event: MouseEvent) => {
  menu.value.toggle(event)
}

defineExpose({
  toggle
})
</script>

<template>
  <!-- Using PrimeVue Menu directly instead of BravoMenu -->
  <Menu
    ref="menu"
    :model="profileMenuItems"
    :popup="true"
    appendTo="body"
    class="profile-menu"
  >
    <template #start>
      <div class="profile-header">
        <div class="avatar-wrapper">
          <img 
            :src="userData.avatar" 
            class="avatar"
            alt="User Avatar"
          />
        </div>
        <div class="user-details">
          <div class="user-name">{{ userData.name }}</div>
          <div class="user-email">{{ userData.email }}</div>
        </div>
      </div>
    </template>
  </Menu>
</template>

<style scoped>
.profile-menu {
  min-width: 250px;
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.avatar-wrapper {
  margin-right: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
  font-size: 15px;

  color: var(--text-color-primary);
}

.user-email {
  font-size: 12px;
  color: var(--text-color-secondary);
}
</style>

<!-- Add a separate script block for TypeScript declarations -->
<script lang="ts">
// TypeScript declarations for the AnnounceKit global object
declare global {
  interface Window {
    announcekit: {
      queue: any[];
      on: (event: string, callback: (data: any) => void) => void;
      push: (config: any) => void;
      widget$product_updates?: {
        open: () => void;
      };
      [key: string]: any;
    };
    OvationMessenger?: {
      show: () => void;
      hide?: () => void;
      [key: string]: any;
    };
  }
}

export default {
  name: 'ProfileMenu'
}
</script> 