<template>
  <div id="announcekit-widget"></div>
</template>
    
<script lang="ts">
export default {
  name: "AnnouncekitWidget",
  mounted() {
    // Define announcekit on the window
    window.announcekit = window.announcekit || { 
      queue: [], 
      on: function(n: any, x: any) { 
        window.announcekit.queue.push([n, x]); 
      }, 
      push: function(x: any) { 
        window.announcekit.queue.push(x); 
      } 
    };
    
    // Initialize the widget with the correct name
    window.announcekit.push({
      widget: "https://changelog.ovationcxm.com/widgets/v2/3pgeBy",
      selector: "#announcekit-widget",
      name: "product_updates",
      defaults: {
        floatWidget: true,
        widgetStyle: { width: '400px' }
      }
    });
    
    // Load the script
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://cdn.announcekit.app/widget-v2.js';
    document.head.appendChild(script);
  }
};
</script>
