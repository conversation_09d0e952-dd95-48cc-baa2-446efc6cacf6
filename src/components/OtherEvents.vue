<script setup lang="ts">
import { ref, onMounted, computed, watchEffect } from 'vue'
import { useCasesStore } from '../stores/cases'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import type { InteractionEvent } from '../services/InteractionEventsAPI'

// Extend the InteractionEvent type to include the id property
type ExtendedInteractionEvent = InteractionEvent & { id: string }

// Define props
interface Props {
  issueId?: string
}

const props = withDefaults(defineProps<Props>(), {
  issueId: undefined
})

const casesStore = useCasesStore()

// Data for the events list
const events = computed(() => casesStore.interactionEvents as ExtendedInteractionEvent[])
const isLoading = computed(() => casesStore.loadingEvents)
const error = computed(() => casesStore.eventsError)

// Format date to a readable string
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString()
}

// Fetch events for the issue
const fetchEvents = async () => {
  try {
    await casesStore.fetchInteractionEvents({
      issueId: props.issueId || casesStore.issueId,
      limit: 50
    })
  } catch (err) {
    console.error('Failed to fetch events:', err)
  }
}

// Refresh events
const refreshEvents = () => {
  fetchEvents()
}

// Watch for changes to issueId
watchEffect(() => {
  if (props.issueId || casesStore.currentIssue?.id) {
    fetchEvents()
  }
})

// Fetch events on component mount
onMounted(async () => {
  if (props.issueId || casesStore.currentIssue?.id) {
    await fetchEvents()
  }
})
</script>

<template>
  <div class="interaction-events">
    <div class="events-header">
      <h2>Other Events</h2>
      <div class="events-actions">
        <BravoButton 
          icon="pi pi-refresh" 
          size="small" 
          @click="refreshEvents"
          :disabled="isLoading"
          aria-label="Refresh Events"
          tooltip="Refresh"
        />
      </div>
    </div>
    
    <div v-if="isLoading" class="loading-state">
      <BravoProgressSpinner />
      <span>Loading events...</span>
    </div>
    
    <div v-else-if="error" class="error-state">
      <p>{{ error }}</p>
      <BravoButton 
        label="Retry" 
        size="small" 
        @click="refreshEvents"
      />
    </div>
    
    <div v-else-if="events.length === 0" class="no-events">
      <p>No interaction events found for this case</p>
    </div>
    
    <ul v-else class="events-list">
      <li v-for="event in events" :key="event.id" class="event-item">
        <div class="event-header">
          <h3 class="event-type">{{ event.category.label }} - {{ event.body.data }}</h3>
          <span class="event-date">{{ formatDate(event.created) }}</span>
        </div>
        <div v-if="event.event_data" class="event-details">
          <!-- Display event data if available -->
          <pre>{{ JSON.stringify(event.event_data, null, 2) }}</pre>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped>
.interaction-events {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--surface-200);
}

.events-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--surface-900);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  flex: 1;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  background-color: var(--red-50);
  color: var(--red-600);
  margin: 1rem;
  border-radius: 4px;
}

.no-events {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--surface-600);
  flex: 1;
}

.events-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.event-item {
  padding: 1rem;
  border-bottom: 1px solid var(--surface-100);
  transition: background-color 0.2s;
}

.event-item:hover {
  background-color: var(--surface-50);
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.event-type {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}

.event-date {
  font-size: 0.875rem;
  color: var(--surface-600);
}

.event-details {
  background-color: var(--surface-50);
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  overflow-x: auto;
}

.event-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}
</style> 