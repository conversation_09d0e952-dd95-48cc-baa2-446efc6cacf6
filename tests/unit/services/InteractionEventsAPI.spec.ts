import { describe, it, expect, vi, beforeEach } from 'vitest'
import { interactionEventsAPI } from '../../../src/services/InteractionEventsAPI'

// Define the mock using vi.hoisted
const mockGet = vi.hoisted(() => vi.fn())

// Mock the httpClient module
vi.mock('../../../src/stores/user', () => {
  return {
    httpClient: {
      get: mockGet
    }
  }
})

describe('InteractionEventsAPI', () => {
  beforeEach(() => {
    // Reset mock before each test
    mockGet.mockReset()
  })

  it('fetches interaction events with the correct params', async () => {
    // Mock a successful response with the correct log_feed property
    const mockResponse = {
      success: true,
      log_feed: {
        results: [
          {
            id: 'event1',
            source_object: 'issues',
            source_object_id: '12345',
            event_type: 'comment_added',
            created: '2023-08-15T10:30:00Z',
            event_data: { comment: 'Test comment' },
            category: { id: 'cat1', label: 'Comment' },
            body: { type: 'text', data: 'Comment data' }
          }
        ]
      }
    }

    // Setup the mock to return our response
    mockGet.mockResolvedValueOnce(mockResponse)

    // Call the API with an issue ID
    const issueId = '12345'
    const result = await interactionEventsAPI.fetchInteractionEvents({ issueId })

    // Verify the httpClient was called with correct parameters
    expect(mockGet).toHaveBeenCalledWith('admin/v4/interaction_events/', {
      'sAction': 'listing',
      page: '1',
      start: '0',
      limit: '25',
      sort: '[{"property":"created","direction":"DESC"}]',
      filter: JSON.stringify([
        { property: 'source_object_id', value: issueId },
        { property: 'source_object', value: 'issues' }
      ])
    })

    // Verify the result is correct
    expect(result).toEqual(mockResponse.log_feed.results)
  })

  it('throws an error if the API call fails', async () => {
    // Mock an error response
    const mockResponse = {
      success: false,
      message: 'Failed to fetch interaction events'
    }

    // Setup the mock to return our error response
    mockGet.mockResolvedValueOnce(mockResponse)

    // Call the API with an issue ID
    const issueId = '12345'
    
    // Verify that it throws an error
    await expect(interactionEventsAPI.fetchInteractionEvents({ issueId }))
      .rejects.toThrow('Failed to fetch interaction events')
  })

  it('allows custom sort parameters', async () => {
    // Mock a successful response with the correct log_feed property
    const mockResponse = {
      success: true,
      log_feed: {
        results: []
      }
    }

    // Setup the mock to return our response
    mockGet.mockResolvedValueOnce(mockResponse)

    // Call the API with custom sort parameters
    const issueId = '12345'
    const sortParams = [
      { property: 'event_type', direction: 'ASC' }
    ]
    
    await interactionEventsAPI.fetchInteractionEvents({ issueId, sort: sortParams })

    // Verify the sort parameter was correctly serialized
    expect(mockGet).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        sort: JSON.stringify(sortParams)
      })
    )
  })

  it('allows additional filter parameters', async () => {
    // Mock a successful response with the correct log_feed property
    const mockResponse = {
      success: true,
      log_feed: {
        results: []
      }
    }

    // Setup the mock to return our response
    mockGet.mockResolvedValueOnce(mockResponse)

    // Call the API with additional filter parameters
    const issueId = '12345'
    const additionalFilters = [
      { property: 'event_type', value: 'comment_added' }
    ]
    
    await interactionEventsAPI.fetchInteractionEvents({ 
      issueId, 
      filter: additionalFilters
    })

    // Verify the filter includes both the issue ID and the additional filters
    const expectedFilters = [
      ...additionalFilters,
      { property: 'source_object_id', value: issueId },
      { property: 'source_object', value: 'issues' }
    ]
    
    expect(mockGet).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        filter: JSON.stringify(expectedFilters)
      })
    )
  })
}) 