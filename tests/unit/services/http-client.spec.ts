import { describe, it, expect, beforeEach } from 'vitest';
import { setupMswForTests, server } from '../../../src/mock-api/test-utils';
import { HttpClient } from '../../../src/services/httpClient';
import { http, HttpResponse } from 'msw';

// Setup MSW for all tests in this file
setupMswForTests();

describe('HttpClient', () => {
    let httpClient: any;

    beforeEach(() => {
        // Reset HTTP client for each test
        httpClient = new HttpClient('http://example.com', 'test-client-id');
    });

    it('should initialize and check authentication status', async () => {
        // Override the default handler for this specific test
        server.use(
            http.get('http://example.com/admin/v4/core/?sAction=userStatus', () => {
                return HttpResponse.json({
                    csrf_token: 'test-csrf-token',
                    users: {
                        email: '<EMAIL>',
                    },
                });
            })
        );

        await httpClient.initialize();
        expect(httpClient.isAuthenticated).toBe(true);
        expect(httpClient.email).toBe('<EMAIL>');
    });

    it('should handle failed authentication', async () => {
        // Override to simulate a failed auth
        server.use(
            http.get('http://example.com/admin/v4/core/?sAction=userStatus', () => {
                return new HttpResponse(null, { status: 401 });
            })
        );

        await httpClient.initialize();
        expect(httpClient.isAuthenticated).toBe(false);
        expect(httpClient.email).toBe('');
    });

    it('should handle login success', async () => {
        // Mock successful login
        server.use(
            http.post('http://example.com/admin/v4/core/?sAction=userLogin', () => {
                return HttpResponse.json({
                    success: true,
                    csrf_token: 'login-csrf-token',
                    message: 'Login successful',
                });
            })
        );

        const result = await httpClient.login({ email: '<EMAIL>', password: 'password' });

        expect(result.success).toBe(true);
        expect(result.message).toBe('Login successful');
        expect(httpClient.isAuthenticated).toBe(true);
    });

    it('should handle login failure', async () => {
        // Mock failed login
        server.use(
            http.post('http://example.com/admin/v4/core/?sAction=userLogin', () => {
                return HttpResponse.json({ success: false, message: 'Invalid credentials' }, { status: 401 });
            })
        );

        try {
            await httpClient.login({ email: '<EMAIL>', password: 'wrong' });
            // If we get here, the test should fail
            expect(true).toBe(false);
        } catch (error) {
            expect(httpClient.isAuthenticated).toBe(false);
        }
    });

    it('should retrieve partner metadata for the logged-in user', async () => {
        // Override default handler for user status to authenticate
        server.use(
            http.get('http://example.com/admin/v4/core/?sAction=userStatus', () => {
                return HttpResponse.json({
                    csrf_token: 'test-csrf-token',
                    users: {
                        email: '<EMAIL>',
                    },
                });
            })
        );

        // Mock partner metadata endpoint
        server.use(
            http.get('http://example.com/admin/v4/core/?sAction=metaPartners', () => {
                return HttpResponse.json({
                    success: true,
                    partners: [
                        {
                            id: '1',
                            name: 'Test Partner',
                            status: 'active',
                        },
                    ],
                });
            })
        );

        // Initialize to authenticate
        await httpClient.initialize();

        // Call the method
        const result = await httpClient.getPartnerMetadata();

        // Assert the result
        expect(result.success).toBe(true);
        expect(result.partners).toBeInstanceOf(Array);
        expect(result.partners.length).toBe(1);
        expect(result.partners[0].name).toBe('Test Partner');
    });
});
