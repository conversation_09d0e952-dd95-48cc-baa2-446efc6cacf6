import { knowledgeAPI } from '../../../src/services/KnowledgeAPI';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Create mock HTTP client with necessary methods
const mockHttpClient = {
    get: vi.fn(),
    post: vi.fn()
};

// Mock the httpClientProvider module
vi.mock('../../../src/services/httpClientProvider', () => ({
    getHttpClient: vi.fn().mockReturnValue(mockHttpClient)
}));

describe('KnowledgeAPI', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('fetchKnowledgeTree', () => {
        it('should fetch and return knowledge tree data', async () => {
            const mockResponse = {
                success: true,
                children: [
                    {
                        id: '1',
                        text: 'Root Node',
                        leaf: false,
                        children: [
                            {
                                id: '1-1',
                                text: 'Child Node',
                                leaf: true,
                            },
                        ],
                    },
                ],
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            const result = await knowledgeAPI.fetchKnowledgeTree();

            expect(mockHttpClient.get).toHaveBeenCalledWith('admin/v4/kb/', {
                sAction: 'listingTree',
                filter: JSON.stringify([
                    { property: 'partner_ids', value: null },
                    { property: 'root_parent_id', value: null },
                ]),
                node: 'root',
            });

            expect(result).toEqual(mockResponse.children);
        });

        it('should throw an error if the API call fails', async () => {
            const errorMessage = 'API error';
            const mockResponse = {
                success: false,
                message: errorMessage,
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            await expect(knowledgeAPI.fetchKnowledgeTree()).rejects.toThrow(errorMessage);
        });

        it('should pass custom filter parameters', async () => {
            const mockResponse = {
                success: true,
                children: [],
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            const customFilter = [{ property: 'custom_property', value: 'custom_value' }];

            await knowledgeAPI.fetchKnowledgeTree({
                filter: customFilter,
                node: 'custom_node',
            });

            expect(mockHttpClient.get).toHaveBeenCalledWith('admin/v4/kb/', {
                sAction: 'listingTree',
                filter: JSON.stringify(customFilter),
                node: 'custom_node',
            });
        });
    });

    describe('fetchKnowledgeItem', () => {
        it('should fetch and return a specific knowledge item', async () => {
            const itemId = '123';
            const mockResponse = {
                success: true,
                data: {
                    id: itemId,
                    title: 'Test Item',
                    content: 'Test content',
                    metadata: {
                        created_at: '2023-01-01T00:00:00Z',
                        updated_at: '2023-01-02T00:00:00Z',
                        tags: ['tag1', 'tag2'],
                    },
                },
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            const result = await knowledgeAPI.fetchKnowledgeItem(itemId);

            expect(mockHttpClient.get).toHaveBeenCalledWith('admin/v4/kb/', {
                sAction: 'read',
                id: itemId,
            });

            expect(result).toEqual(mockResponse);
            expect(result.data?.title).toBe('Test Item');
            expect(result.data?.metadata?.tags).toContain('tag1');
        });

        it('should throw an error if the API call fails', async () => {
            const itemId = '123';
            const errorMessage = 'Item not found';
            const mockResponse = {
                success: false,
                message: errorMessage,
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            await expect(knowledgeAPI.fetchKnowledgeItem(itemId)).rejects.toThrow(errorMessage);
        });
    });

    describe('fetchKnowledgeListing', () => {
        it('should fetch and return knowledge listing data', async () => {
            const mockItems = [
                {
                    id: '1',
                    title: 'Article 1',
                    content: 'Content 1',
                    type: 'Article',
                    status: 'published',
                    access: 'public',
                    updatedAt: '2023-01-01T00:00:00Z',
                },
                {
                    id: '2',
                    title: 'Article 2',
                    content: 'Content 2',
                    type: 'Article',
                    status: 'draft',
                    access: 'internal',
                    updatedAt: '2023-01-02T00:00:00Z',
                },
            ];

            const mockResponse = {
                success: true,
                data: {
                    items: mockItems,
                    total: 25, // Total count from API
                },
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            const result = await knowledgeAPI.fetchKnowledgeListing();

            expect(mockHttpClient.get).toHaveBeenCalledWith('admin/v4/kb/', {
                sAction: 'listing',
                page: '1',
                start: '0',
                limit: '50',
                filter: JSON.stringify([
                    { property: 'type', value: 0 },
                    { property: 'id', value: '_no_filter_' },
                    { property: 'parent_id', value: null },
                    { property: 'partner_ids', operator: 'intersect_set', value: null },
                    { property: 'visibility', operator: '=', value: null },
                    { property: 'status', operator: '=', value: null },
                    { property: 'owner_partner_id', value: '_no_filter_' },
                    { property: 'dict_id', value: null },
                    { property: 'tag_ids', value: null },
                ]),
            });

            // Check KnowledgeListingResult format
            expect(result.items).toEqual(mockItems);
            expect(result.totalCount).toBe(0);
            expect(result.items.length).toBe(2);
            expect(result.items[0].title).toBe('Article 1');
            expect(result.items[1].status).toBe('draft');
        });

        it('should pass custom listing parameters', async () => {
            const mockResponse = {
                success: true,
                data: {
                    items: [],
                    total: 0,
                },
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            const customFilter = [{ property: 'root_parent_id', value: '123' }];

            const result = await knowledgeAPI.fetchKnowledgeListing({
                page: 2,
                start: 10,
                limit: 25,
                filter: customFilter,
            });

            expect(mockHttpClient.get).toHaveBeenCalledWith('admin/v4/kb/', {
                sAction: 'listing',
                page: '2',
                start: '10',
                limit: '25',
                filter: JSON.stringify(customFilter),
            });

            // Check that empty result is properly structured
            expect(result.items).toEqual([]);
            expect(result.totalCount).toBe(0);
        });

        it('should throw an error if the API call fails', async () => {
            const errorMessage = 'Failed to fetch listing';
            const mockResponse = {
                success: false,
                message: errorMessage,
            };

            mockHttpClient.get.mockResolvedValue(mockResponse);

            await expect(knowledgeAPI.fetchKnowledgeListing()).rejects.toThrow(errorMessage);
        });
    });

    describe('putRevisions', () => {
        it('should update knowledge revisions successfully', async () => {
            const mockRevisions = [
                {
                    id: '37a86f00-d4d4-4098-a85b-87b4e6351e3b',
                    body: '<bt/><div style="font-size: 14px; font-family: Inter;"><div class="tabs-panel"><div class="tabs"><p class="tab-title"><a href="#panel1">Place pointer here and edit text for Tab 1</a></p><p class="tab-title"><a href="#panel2">Place pointer here and edit text for Tab 2</a></p><p class="tab-title"><a href="#panel3">Place pointer here and edit text for Tab 3</a></p></div><div class="tabs-content"><div id="panel1" class="content"><p>Update this content f…t" id="panel4"><p>Upd4444r Tab 4</p></div><div class="content" id="panel5"><p>Upd55555t for Tab 5</p></div><div class="content" id="panel6"><p>66666for Tab 6</p></div></div></div><p><br></p><img alt="73b46f1a334a9d85f37f9202b4d48c91.png" class="fr-fic fr-dib fr-rounded" src="https://api.stage.goboomtown.com/upload_files/73b46f1a334a9d85f37f9202b4d48c91.png"></figure><p><br></p><p><br></p><p><br></p><p><br></p><p><br></p></div>',
                },
            ];

            const mockResponse = {
                success: true,
                message: 'Revisions updated successfully',
            };

            mockHttpClient.post.mockResolvedValue(mockResponse);

            const result = await knowledgeAPI.putRevisions(mockRevisions);

            expect(mockHttpClient.post).toHaveBeenCalledWith(
                'admin/v4/kb/',
                { kb_revisions: JSON.stringify(mockRevisions) },
                { sAction: 'revisionsPut' }
            );

            expect(result).toEqual(mockResponse);
            expect(result.success).toBe(true);
        });

        it('should throw an error if the API call fails', async () => {
            const mockRevisions = [
                {
                    id: '37a86f00-d4d4-4098-a85b-87b4e6351e3b',
                    body: '<bt/><div>Test content</div>',
                },
            ];

            const errorMessage = 'Failed to update revisions';
            const mockResponse = {
                success: false,
                message: errorMessage,
            };

            mockHttpClient.post.mockResolvedValue(mockResponse);

            await expect(knowledgeAPI.putRevisions(mockRevisions)).rejects.toThrow(errorMessage);
        });
    });
});
