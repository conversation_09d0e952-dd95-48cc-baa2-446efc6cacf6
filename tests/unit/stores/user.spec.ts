import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createP<PERSON>, setActive<PERSON><PERSON> } from 'pinia';
import { useUserStore } from '@/stores/user';

describe('User Store', () => {
    beforeEach(() => {
        // Create a fresh pinia instance for each test
        setActivePinia(createPinia());
    });

    describe('Permission Functions', () => {
        it('should return false for hasPermission when no user data exists', () => {
            const store = useUserStore();
            expect(store.hasPermission('some_permission')).toBe(false);
        });

        it('should check permissions in userData.perms array', () => {
            const store = useUserStore();
            // Set mock user data with perms array
            store.userData = {
                perms: ['issue_view', 'issue_edit', 'cust_view'],
            };

            expect(store.hasPermission('issue_view')).toBe(true);
            expect(store.hasPermission('issue_edit')).toBe(true);
            expect(store.hasPermission('cust_view')).toBe(true);
            expect(store.hasPermission('admin_access')).toBe(false);
        });

        it('should check permissions in userData.users.perms array', () => {
            const store = useUserStore();
            // Set mock user data with perms in users object
            store.userData = {
                users: {
                    perms: ['issue_view', 'issue_edit', 'cust_view'],
                },
            };

            expect(store.hasPermission('issue_view')).toBe(true);
            expect(store.hasPermission('issue_edit')).toBe(true);
            expect(store.hasPermission('cust_view')).toBe(true);
            expect(store.hasPermission('admin_access')).toBe(false);
        });

        it('should check permissions in permSet object', () => {
            const store = useUserStore();
            // Set mock user data with permSet object
            store.userData = {
                perms: {
                    permSet: {
                        issue_view: 'issue_view',
                        issue_edit: 'issue_edit',
                        cust_view: 'cust_view',
                    },
                },
            };

            expect(store.hasPermission('issue_view')).toBe(true);
            expect(store.hasPermission('issue_edit')).toBe(true);
            expect(store.hasPermission('cust_view')).toBe(true);
            expect(store.hasPermission('admin_access')).toBe(false);
        });

        it('should return empty array from getUserPermissions when no user data exists', () => {
            const store = useUserStore();
            expect(store.getUserPermissions()).toEqual([]);
        });

        it('should get all permissions from perms array', () => {
            const store = useUserStore();
            const expectedPerms = ['issue_view', 'issue_edit', 'cust_view'];

            // Set mock user data with perms array
            store.userData = { perms: expectedPerms };

            expect(store.getUserPermissions()).toEqual(expectedPerms);
        });

        it('should get all permissions from permSet object', () => {
            const store = useUserStore();
            const permSetObj = {
                issue_view: 'issue_view',
                issue_edit: 'issue_edit',
                cust_view: 'cust_view',
            };

            // Set mock user data with permSet object
            store.userData = { perms: { permSet: permSetObj } };

            expect(store.getUserPermissions()).toEqual(Object.keys(permSetObj));
        });

        it('should check if user has any permission', () => {
            const store = useUserStore();
            // Set mock user data with perms array
            store.userData = { perms: ['issue_view', 'issue_edit'] };

            expect(store.hasAnyPermission(['issue_view', 'admin_access'])).toBe(true);
            expect(store.hasAnyPermission(['admin_access', 'super_user'])).toBe(false);
        });

        it('should check if user has all permissions', () => {
            const store = useUserStore();
            // Set mock user data with perms array
            store.userData = { perms: ['issue_view', 'issue_edit', 'cust_view'] };

            expect(store.hasAllPermissions(['issue_view', 'issue_edit'])).toBe(true);
            expect(store.hasAllPermissions(['issue_view', 'admin_access'])).toBe(false);
        });
    });
});
