import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import InboxView from '../../../src/modules/inbox/views/InboxView.vue';
import { createPinia, setActivePinia } from 'pinia';
import type { SidebarItem } from '../../../src/modules/inbox/components/BravoInboxSidebar.vue';

// Mock the casesStore
const mockFetchCurrentIssue = vi.fn().mockResolvedValue(undefined);
vi.mock('../../../src/stores/cases', () => ({
    useCasesStore: vi.fn(() => ({
        fetchCurrentIssue: mockFetchCurrentIssue,
        fetchCases: vi.fn(),
        fetchViews: vi.fn(),
        currentIssue: {
            id: 'issue123',
            display_name: 'Test Issue',
            availableComm: [1, 2, 3],
            source_data: { source: 'email' },
        },
        loading: false,
        error: null,
        issueId: 'issue123',
    })),
}));

// Mock the child components
vi.mock('../../../src/modules/inbox/components/BravoInboxSidebar.vue', () => ({
    default: {
        name: 'BravoInboxSidebar',
        template: '<div class="mock-sidebar"></div>',
    },
}));

vi.mock('../../../src/components/ViewCard.vue', () => ({
    default: {
        name: 'ViewCard',
        template: '<div class="mock-view-card"></div>',
        props: ['selectedView'],
    },
}));

vi.mock('../../../src/components/BravoEventsList.vue', () => ({
    default: {
        name: 'BravoEventsList',
        template: '<div class="mock-events-list"></div>',
        props: ['issueId'],
    },
}));

vi.mock('../../../src/modules/inbox/components/IssueDetails.vue', () => ({
    default: {
        name: 'IssueDetails',
        template: '<div class="mock-issue-details"></div>',
        props: ['onBack', 'onViewEvents'],
    },
}));

describe('InboxView', () => {
    let wrapper: VueWrapper;

    beforeEach(() => {
        // Clear mock calls
        vi.clearAllMocks();

        // Setup Pinia store
        setActivePinia(createPinia());

        // Mount the component
        wrapper = mount(InboxView);
    });

    it('fetches current issue when mounted', () => {
        expect(mockFetchCurrentIssue).toHaveBeenCalledWith('issue123');
    });

    it('renders header with buttons', () => {
        expect(wrapper.find('.inbox-header').exists()).toBe(true);
        expect(wrapper.findAll('.header-actions .p-button').length).toBe(2);
    });

    it('renders ViewCard by default', () => {
        expect(wrapper.find('.mock-view-card').exists()).toBe(true);
        expect(wrapper.find('.mock-issue-details').exists()).toBe(false);
        expect(wrapper.find('.mock-events-list').exists()).toBe(false);
    });

    it('switches to events view when toggleEventsView is called', async () => {
        // Get the component instance
        const viewVM = wrapper.vm as any;

        // Set state to show events
        viewVM.showingEvents = true;
        await wrapper.vm.$nextTick();

        // Check if events list is displayed
        expect(wrapper.find('.mock-events-list').exists()).toBe(true);
        expect(wrapper.find('.mock-view-card').exists()).toBe(false);
    });

    it('switches to issue details when showingIssue is true', async () => {
        // Get the component instance
        const viewVM = wrapper.vm as any;

        // Set state to show issue details
        viewVM.showingIssue = true;
        viewVM.showingEvents = false;
        await wrapper.vm.$nextTick();

        // Check if issue details are displayed
        expect(wrapper.find('.mock-issue-details').exists()).toBe(true);
        expect(wrapper.find('.mock-view-card').exists()).toBe(false);
        expect(wrapper.find('.mock-events-list').exists()).toBe(false);
    });

    it('handles select-item event from sidebar', async () => {
        // Get the component instance
        const viewVM = wrapper.vm as any;

        // Mock a sidebar item
        const mockItem: SidebarItem = {
            id: 'view1',
            label: 'Test View',
            count: 0,
            icon: '📄',
            type: 'item',
        };

        // Call handleSelectItem manually
        viewVM.handleSelectItem(mockItem);
        await wrapper.vm.$nextTick();

        // Check if selected view was updated
        expect(viewVM.selectedView).toEqual(mockItem);
        expect(viewVM.showingIssue).toBe(false);
        expect(viewVM.showingEvents).toBe(false);
    });

    it('handles select-issue event from ViewCard', async () => {
        // Get the component instance
        const viewVM = wrapper.vm as any;

        // Mock an issue
        const mockIssue = {
            id: 'issue456',
            display_name: 'New Issue',
        };

        // Call handleSelectIssue manually
        await viewVM.handleSelectIssue(mockIssue);

        // Check if currentIssue was fetched and showingIssue is true
        expect(mockFetchCurrentIssue).toHaveBeenCalledWith('issue456');
        expect(viewVM.showingIssue).toBe(true);
    });
});
