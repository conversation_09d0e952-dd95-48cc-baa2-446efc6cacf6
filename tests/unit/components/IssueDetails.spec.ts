import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import IssueDetails from '../../../src/modules/inbox/components/IssueDetails.vue'
import { createPinia, setActivePinia } from 'pinia'

// Mock the casesStore
const mockFetchCurrentIssue = vi.fn().mockResolvedValue(undefined)
vi.mock('../../../src/stores/cases', () => ({
  useCasesStore: vi.fn(() => ({
    fetchCurrentIssue: mockFetchCurrentIssue,
    currentIssue: {
      id: 'issue123',
      display_name: 'Test Issue',
      availableComm: [1, 2, 3], 
      source_data: { source: 'email' }
    },
    loading: false,
    error: null
  }))
}))

describe('IssueDetails', () => {
  let wrapper: VueWrapper
  const onBackMock = vi.fn()
  const onViewEventsMock = vi.fn()

  beforeEach(() => {
    // Clear mock calls
    vi.clearAllMocks()
    
    // Setup Pinia store
    setActivePinia(createPinia())
    
    // Mount the component with default props
    wrapper = mount(IssueDetails, {
      props: {
        onBack: onBackMock,
        onViewEvents: onViewEventsMock
      }
    })
  })

  it('displays issue details from the store', async () => {
    // Wait for component to render
    await wrapper.vm.$nextTick()
    
    // Check if issue details are displayed correctly
    expect(wrapper.find('.issue-header h2').text()).toContain('Test Issue')
    expect(wrapper.find('.issue-field:nth-child(1)').text()).toContain('issue123')
    expect(wrapper.find('.issue-field:nth-child(2)').text()).toContain('email')
    expect(wrapper.find('.issue-field:nth-child(3)').text()).toContain('3')
  })

  it('calls onBack when back link is clicked', async () => {
    // Find and click the back link
    await wrapper.find('.back-link').trigger('click')
    
    // Check if onBack was called
    expect(onBackMock).toHaveBeenCalled()
  })

  it('calls onViewEvents when View Events button is clicked', async () => {
    // Find and click the View Events button
    await wrapper.find('.issue-actions .p-button').trigger('click')
    
    // Check if onViewEvents was called
    expect(onViewEventsMock).toHaveBeenCalled()
  })

  it('fetches issue details when issueId prop is provided', async () => {
    // Create a new wrapper with issueId prop
    const wrapper = mount(IssueDetails, {
      props: {
        issueId: 'issue456',
        onBack: onBackMock,
        onViewEvents: onViewEventsMock
      }
    })
    
    // Wait for component to render
    await wrapper.vm.$nextTick()
    
    // Check if fetchCurrentIssue was called with the correct issueId
    expect(mockFetchCurrentIssue).toHaveBeenCalledWith('issue456')
  })

  it.skip('shows loading state when store is loading', async () => {
    // Create a mock casesStore with loading state
    const mockStore = {
      currentIssue: null,
      loading: true,
      error: null,
      fetchCurrentIssue: mockFetchCurrentIssue
    }
    
    // Get the component instance
    const issueDetailsVM = wrapper.vm as any
    
    // Replace the casesStore with our mock store
    issueDetailsVM.casesStore = mockStore
    
    // Wait for component to update
    await wrapper.vm.$nextTick()
    
    // Check if loading state is displayed
    expect(wrapper.find('.loading-state').exists()).toBe(true)
  })

  it.skip('shows error state when store has error', async () => {
    // Create a mock casesStore with error state
    const mockStore = {
      currentIssue: null,
      loading: false,
      error: 'Failed to load issue',
      fetchCurrentIssue: mockFetchCurrentIssue
    }
    
    // Get the component instance
    const issueDetailsVM = wrapper.vm as any
    
    // Replace the casesStore with our mock store
    issueDetailsVM.casesStore = mockStore
    
    // Wait for component to update
    await wrapper.vm.$nextTick()
    
    // Check if error state is displayed
    expect(wrapper.find('.error-state').exists()).toBe(true)
    expect(wrapper.find('.error-state').text()).toContain('Failed to load issue')
  })
}) 