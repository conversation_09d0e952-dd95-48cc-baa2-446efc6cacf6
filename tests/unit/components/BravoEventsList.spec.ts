import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createTesting<PERSON>inia } from '@pinia/testing';
import BravoEventsList from '../../../src/components/BravoEventsList.vue';
import { useCasesStore } from '../../../src/stores/cases';
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';

// Mock the Bravo components
vi.mock('bravo-component-library/components/BravoProgressSpinner.vue', () => ({
    default: {
        name: 'BravoProgressSpinner',
        template: '<div data-test="progress-spinner"></div>',
    },
}));

// Create a hoisted mock for BravoButton
const BravoButtonMock = vi.hoisted(() => vi.fn());

// Mock the BravoButton component
vi.mock('@services/ui-component-library/components/BravoButton.vue', () => ({
    default: {
        name: 'BravoButton',
        template: '<button @click="$emit(\'click\')"><slot></slot>{{ label }}</button>',
        props: ['label', 'icon', 'disabled', 'size', 'tooltip', 'aria-label', 'ariaLabel'],
        emits: ['click'],
        setup(props) {
            BravoButtonMock(props, null);
            return {};
        },
    },
}));

describe('BravoEventsList', () => {
    const mockEvents = [
        {
            id: '1',
            category: { id: 'cat1', label: 'Email' },
            body: { type: 'sent', data: 'Email sent' },
            created: '2024-03-20T10:00:00Z',
            event_data: { recipient: '<EMAIL>' },
        },
        {
            id: '2',
            category: { id: 'cat2', label: 'Note' },
            body: { type: 'created', data: 'Note added' },
            created: '2024-03-20T11:00:00Z',
        },
    ];

    let wrapper: ReturnType<typeof mount>;
    let store: ReturnType<typeof useCasesStore>;

    beforeEach(() => {
        // Reset the mock implementation
        BravoButtonMock.mockReset();
        BravoButtonMock.mockImplementation((props) => ({
            name: 'BravoButton',
            template: '<button :disabled="props.disabled"><slot></slot>{{ props.label }}</button>',
            props: ['label', 'icon', 'disabled', 'size', 'tooltip'],
        }));

        // Create a fresh pinia instance for each test
        const pinia = createTestingPinia({
            createSpy: vi.fn,
            initialState: {
                cases: {
                    interactionEvents: [],
                    loadingEvents: false,
                    eventsError: null,
                    currentIssue: { id: 'test-issue' },
                },
            },
        });

        wrapper = mount(BravoEventsList, {
            global: {
                plugins: [pinia],
                stubs: {
                    BravoProgressSpinner: true,
                },
            },
        });

        store = useCasesStore();
        // Reset the spy count after mount
        vi.mocked(store.fetchInteractionEvents).mockClear();
    });

    it('renders properly', () => {
        expect(wrapper.exists()).toBe(true);
        expect(wrapper.find('.events-header h2').text()).toBe('Interaction Events');
    });

    it('shows loading state', async () => {
        store.loadingEvents = true;
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.loading-state').exists()).toBe(true);
        expect(wrapper.findComponent(BravoProgressSpinner).exists()).toBe(true);
    });

    it('shows error state and retry button', async () => {
        store.eventsError = 'Failed to load events';
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.error-state').exists()).toBe(true);
        expect(wrapper.find('.error-state p').text()).toBe('Failed to load events');

        // Verify both buttons are rendered correctly
        expect(BravoButtonMock).toHaveBeenCalledWith(
            expect.objectContaining({
                icon: 'pi pi-refresh',
                size: 'small',
                ariaLabel: 'Refresh Events',
                tooltip: 'Refresh',
                disabled: false,
            }),
            null
        );

        expect(BravoButtonMock).toHaveBeenCalledWith(
            expect.objectContaining({
                label: 'Retry',
                size: 'small',
            }),
            null
        );
    });

    it('shows empty state when no events', async () => {
        store.interactionEvents = [];
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.no-events').exists()).toBe(true);
        expect(wrapper.find('.no-events p').text()).toBe('No interaction events found for this case');
    });

    it('renders events list correctly', async () => {
        store.interactionEvents = mockEvents;
        await wrapper.vm.$nextTick();

        const eventItems = wrapper.findAll('.event-item');
        expect(eventItems).toHaveLength(2);

        // Check first event
        expect(eventItems[0].find('.event-type').text()).toBe('Email - Email sent');
        expect(eventItems[0].find('.event-details').exists()).toBe(true);

        // Check second event
        expect(eventItems[1].find('.event-type').text()).toBe('Note - Note added');
        expect(eventItems[1].find('.event-details').exists()).toBe(false);
    });

    it('fetches events on mount', async () => {
        // Create a new wrapper to test initial mount behavior
        const newWrapper = mount(BravoEventsList, {
            global: {
                plugins: [
                    createTestingPinia({
                        createSpy: vi.fn,
                        initialState: {
                            cases: {
                                currentIssue: { id: 'test-issue' },
                            },
                        },
                    }),
                ],
                stubs: {
                    BravoProgressSpinner: true,
                },
            },
        });

        const newStore = useCasesStore();
        await flushPromises();

        expect(newStore.fetchInteractionEvents).toHaveBeenCalledTimes(1);
        expect(newStore.fetchInteractionEvents).toHaveBeenCalledWith({
            issueId: newStore.issueId,
            limit: 50,
        });
    });

    it('fetches events when issueId prop changes', async () => {
        await wrapper.setProps({ issueId: 'new-issue' });
        await flushPromises();

        expect(store.fetchInteractionEvents).toHaveBeenCalledWith({
            issueId: 'new-issue',
            limit: 50,
        });
    });

    it('refreshes events when refresh button is clicked', async () => {
        // Mock the store method to resolve immediately
        vi.mocked(store.fetchInteractionEvents).mockResolvedValueOnce();

        // Find and click the refresh button in the header
        const refreshButton = wrapper.find('.events-actions button');
        await refreshButton.trigger('click');
        await flushPromises();

        expect(store.fetchInteractionEvents).toHaveBeenCalledTimes(1);
        expect(store.fetchInteractionEvents).toHaveBeenCalledWith({
            issueId: store.issueId,
            limit: 50,
        });
    });

    it('formats date correctly', async () => {
        store.interactionEvents = [mockEvents[0]];
        await wrapper.vm.$nextTick();

        const formattedDate = new Date(mockEvents[0].created).toLocaleString();
        const dateElement = wrapper.find('.event-date');
        expect(dateElement.exists()).toBe(true);
        expect(dateElement.text()).toBe(formattedDate);
    });

    it('disables refresh button during loading', async () => {
        store.loadingEvents = true;
        await wrapper.vm.$nextTick();

        // Verify the refresh button is disabled with all its properties
        expect(BravoButtonMock).toHaveBeenCalledWith(
            expect.objectContaining({
                icon: 'pi pi-refresh',
                size: 'small',
                disabled: true,
                ariaLabel: 'Refresh Events',
                tooltip: 'Refresh',
            }),
            null
        );
    });
});
