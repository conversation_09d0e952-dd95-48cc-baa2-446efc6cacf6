import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import KnowledgeSidePanel from '../../../src/modules/knowledge/components/KnowledgeSidePanel.vue';
import { useKnowledgeStore } from '../../../src/modules/knowledge/stores/knowledge';
import { createTestingPinia } from '@pinia/testing';
import { createI18n } from 'vue-i18n';

// Create a mock i18n instance
const i18n = createI18n({
    legacy: false,
    locale: 'en',
    messages: {
        en: {
            knowledge: {
                sidePanel: {
                    search: 'Search',
                    addNew: 'Add New',
                    libraries: 'Libraries',
                    retry: 'Retry',
                },
            },
        },
    },
});

// Mock the Tree component
vi.mock('primevue/tree', () => ({
    default: {
        name: 'Tree',
        props: {
            value: Array,
            selectionMode: String,
            selectionKeys: Object,
            expandedKeys: Object,
        },
        template: '<div class="p-tree" data-test="tree-component"><slot /></div>',
    },
}));

// Mock the Dropdown component
vi.mock('primevue/dropdown', () => ({
    default: {
        name: 'Dropdown',
        props: {
            modelValue: Object,
            options: Array,
            optionLabel: String,
        },
        template:
            '<div class="p-dropdown" data-test="dropdown-component"><slot name="value" :value="modelValue" /><slot name="option" :option="options[0]" /></div>',
    },
}));

// Mock PrimeVue directives
const mockDirectives = {
    tooltip: {
        mounted: vi.fn(),
        updated: vi.fn(),
        unmounted: vi.fn(),
    },
};

describe('KnowledgeSidePanel.vue', () => {
    let wrapper;
    let store;

    beforeEach(() => {
        // Create pinia store with mocks
        const pinia = createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
        });

        // Get the store instance before mounting
        store = useKnowledgeStore(pinia);

        // Mock the store actions and state properties
        store.fetchKnowledgeTree = vi.fn().mockResolvedValue([]);
        store.fetchKnowledgeItem = vi.fn().mockResolvedValue({});
        store.loading = false;
        store.error = null;

        // Mount component after setting up store and i18n
        wrapper = mount(KnowledgeSidePanel, {
            global: {
                plugins: [pinia, i18n],
                directives: mockDirectives,
                stubs: {
                    Tree: true,
                    Dropdown: true,
                    InputText: true,
                    Button: true,
                },
            },
        });
    });

    it('renders the component with correct structure', () => {
        expect(wrapper.find('.side-panel').exists()).toBe(true);
        expect(wrapper.find('.panel-header').exists()).toBe(true);
        expect(wrapper.find('.panel-content').exists()).toBe(true);
        expect(wrapper.find('.libraries-heading').exists()).toBe(true);
    });

    it('fetches knowledge tree data on mount', () => {
        expect(store.fetchKnowledgeTree).toHaveBeenCalled();
    });

    it('shows loading state when loading', async () => {
        // Set loading state in store
        store.loading = true;

        // Wait for the component to update
        await wrapper.vm.$nextTick();

        // The component should be affected by isLoading computed property
        expect(wrapper.vm.isLoading).toBe(true);
    });

    it('shows error state when store has an error', async () => {
        // Set error in store
        store.error = 'Test error message';

        // Wait for the component to update
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.error-state').exists()).toBe(true);
        expect(wrapper.find('.error-state').text()).toContain('Test error message');
        expect(wrapper.find('.retry-button').exists()).toBe(true);
    });

    it('retries fetching data when retry button is clicked', async () => {
        // Set error in store
        store.error = 'Test error message';

        // Wait for the component to update
        await wrapper.vm.$nextTick();

        // Clear mock to check if it's called again
        store.fetchKnowledgeTree.mockClear();

        // Click retry button
        await wrapper.find('.retry-button').trigger('click');

        expect(store.fetchKnowledgeTree).toHaveBeenCalled();
    });

    it('fetches knowledge tree when library changes', async () => {
        // Clear the mock to check if it's called again
        store.fetchKnowledgeTree.mockClear();

        // Update selected library
        store.selectedLibrary = { name: 'Product', code: 'prod' };

        // Trigger watcher
        await wrapper.vm.$nextTick();

        expect(store.fetchKnowledgeTree).toHaveBeenCalled();
    });
});
