import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { nextTick, ref } from 'vue';
import { ArticleStatus } from '../../../src/modules/knowledge/enums/ArticleStatus';

// Mock components
vi.mock('@services/ui-component-library/components/BravoInputText.vue', () => ({
  default: {
    name: 'BravoInputText',
    template: '<input data-testid="bravo-input" />',
  },
}));

vi.mock('@services/ui-component-library/components/BravoButton.vue', () => ({
  default: {
    name: 'BravoButton',
    template: '<button><slot /></button>',
    props: ['label', 'icon', 'severity', 'outlined', 'text', 'disabled'],
  },
}));

// Mock stores
vi.mock('../../../src/modules/knowledge/stores/knowledge.ts', () => ({
  useKnowledgeStore: vi.fn(() => ({
    fetchArticleById: vi.fn(),
    getItemById: vi.fn(),
    currentArticle: {
      id: '123',
      title: 'Test Article',
      content: 'Test content',
      _uiAccess: { edit: true },
    },
    updateItem: vi.fn(),
    clearCurrentArticle: vi.fn(),
    publishRevision: vi.fn().mockResolvedValue({}),
  })),
}));

vi.mock('../../../stores/user.ts', () => ({
  useUserStore: vi.fn(() => ({
    hasPermission: vi.fn(() => true),
    getPartnerLabelById: vi.fn((id) => `User ${id}`),
  })),
}));

// Create mocks for route and router
const mockRoute = vi.fn(() => ({
  params: { id: '123' },
  query: {},
}));

const mockRouter = vi.fn(() => ({
  push: vi.fn(),
}));

// Mock router
vi.mock('vue-router', () => ({
  useRoute: mockRoute,
  useRouter: mockRouter,
  createRouter: vi.fn(() => ({
    beforeEach: vi.fn(),
    afterEach: vi.fn(),
    push: vi.fn(),
  })),
  createWebHistory: vi.fn(),
}));

// Mock API
vi.mock('../../../src/services/KnowledgeAPI', () => ({
  knowledgeAPI: {
    fetchArticleRevisions: vi.fn().mockResolvedValue({
      items: [
        {
          id: 'rev1',
          isDraft: true,
          isPublished: false,
          isActive: true,
          updated: new Date().toISOString(),
        },
      ],
      totalCount: 1,
    }),
    fetchRevisionById: vi.fn(),
    unpublishArticles: vi.fn().mockResolvedValue({ success: true }),
    fetchRelatedArticles: vi.fn().mockResolvedValue({ items: [], totalCount: 0 }),
    getKBsTree: vi.fn().mockResolvedValue([]),
    getTokens: vi.fn().mockResolvedValue([]),
    fetchKnowledgeFileManager: vi.fn().mockResolvedValue({ items: [], totalCount: 0 }),
    postFileManagerFile: vi.fn().mockResolvedValue({}),
    deleteFileManagerFile: vi.fn().mockResolvedValue({}),
  },
}));

// Mock i18n
vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: (key: string) => key,
  })),
}));

// Mock PrimeVue useToast
vi.mock('primevue/usetoast', () => ({
  useToast: vi.fn(() => ({
    add: vi.fn(),
  })),
}));

// Mock PrimeVue useConfirm
vi.mock('primevue/useconfirm', () => ({
  useConfirm: vi.fn(() => ({
    require: vi.fn(),
  })),
}));

// Mock froala
vi.mock('../../../../src/froala/froala-config.ts', () => ({
  default: {
    events: {},
  },
}));

// Mock Select component
vi.mock('primevue/select', () => ({
  default: {
    name: 'Select',
    template: '<div class="p-select"><slot name="value" :value="modelValue" /></div>',
    props: ['modelValue', 'options', 'optionLabel', 'disabled'],
  },
}));

// Mock Menu component
vi.mock('primevue/menu', () => ({
  default: {
    name: 'Menu',
    template: '<div class="p-menu"></div>',
    props: ['model', 'popup'],
    methods: {
      toggle: vi.fn(),
    },
  },
}));

// Mock Tooltip directive
vi.mock('primevue/tooltip', () => ({
  default: {},
}));

// Mock froala component
vi.mock('@froala/vue-froala-wysiwyg', () => ({
  default: {
    name: 'froala',
    template: '<div class="froala-editor"></div>',
    props: ['tag', 'config', 'value'],
  },
}));

// Mock FileManager component
vi.mock('@/components/FileManager', () => ({
  default: {
    name: 'FileManager',
    template: '<div class="file-manager"></div>',
    props: ['show', 'files', 'isLoading', 'activeTab', 'uploadProgress'],
    emits: ['file-selected', 'tab-changed', 'search', 'delete'],
  },
}));

// Mock useFileManager composable
vi.mock('@/composables/useFileManager', () => ({
  useFileManager: vi.fn(() => ({
    showFileManager: ref(false),
    isLoadingFiles: ref(false),
    uploadProgress: ref(0),
    activeTab: ref('my_files'),
    currentPage: ref(1),
    searchQuery: ref(''),
    fileManagerItems: ref([]),
    totalFileCount: ref(0),
    fetchFilesForTab: vi.fn(),
    uploadFile: vi.fn(),
    handleFileManagerPageChange: vi.fn(),
    deleteFile: vi.fn(),
  })),
}));

describe('ArticleView Buttons', () => {
  let wrapper: any;

  beforeEach(async () => {
    // Setup Pinia
    setActivePinia(createPinia());

    // Mock window.confirm
    vi.spyOn(window, 'confirm').mockImplementation(() => true);

    // Import the component
    const ArticleView = (await import('../../../src/modules/knowledge/views/ArticleView.vue')).default;

    // Create data to be used in the component
    const initialData = {
      selectedRevision: {
        status: ArticleStatus.DRAFT,
        name: 'Draft',
        code: 'rev1',
      },
      isEditing: false,
      article: {
        id: '123',
        title: 'Test Article',
        content: 'Test content',
        _uiAccess: { edit: true },
      },
    };

    // Mount the component with setup function to provide initial data
    wrapper = mount(ArticleView, {
      props: {
        id: '123',
      },
      global: {
        stubs: {
          BravoInputText: true,
          BravoButton: true,
          BravoTitlePage: true,
          BravoBody: true,
          Select: true,
          Menu: true,
          froala: true,
          ArticleSidebar: true,
          DeleteArticleModal: true,
          Popover: true,
          Tree: true,
          ToggleSwitch: true,
          FileManager: true,
        },
        directives: {
          tooltip: { mounted: vi.fn(), unmounted: vi.fn() },
        },
      },
      data() {
        return initialData;
      },
    });

    // Wait for component to load
    await flushPromises();
    await nextTick();
  });

  // Test Case 1: Publish button is visible in view mode for DRAFT revisions
  it('shows Publish button when article revision is DRAFT in view mode', async () => {
    // Ensure we're in view mode
    await wrapper.setData({ isEditing: false });
    await nextTick();

    // Find the publish button
    const publishButton = wrapper.find('[data-testid="publish-article-button"]');

    // Verify the button is visible
    expect(publishButton.exists()).toBe(true);
  });

  // Test Case 2: Verify template logic - Publish button should not be visible in edit mode
  it('template logic hides Publish button in edit mode', async () => {
    // Create a simplified test component to verify the template logic
    const TestComponent = {
      template: `
        <div>
          <template v-if="isEditing">
            <!-- Edit mode buttons -->
            <button data-testid="cancel-edit-button">Cancel</button>
            <button data-testid="save-edit-button">Save</button>
          </template>
          <template v-else>
            <!-- View mode buttons -->
            <button data-testid="article-menu-button">Menu</button>
            <template v-if="selectedRevision.status === 'DRAFT'">
              <button data-testid="publish-article-button">Publish</button>
              <button data-testid="edit-article-button">Edit</button>
            </template>
          </template>
        </div>
      `,
      data() {
        return {
          isEditing: false,
          selectedRevision: {
            status: 'DRAFT',
            name: 'Draft',
            code: 'rev1',
          }
        };
      }
    };

    // Mount the test component
    const testWrapper = mount(TestComponent);

    // Verify Publish button is visible in view mode
    expect(testWrapper.find('[data-testid="publish-article-button"]').exists()).toBe(true);

    // Set to edit mode
    await testWrapper.setData({ isEditing: true });
    await nextTick();

    // Verify Publish button is NOT visible in edit mode
    expect(testWrapper.find('[data-testid="publish-article-button"]').exists()).toBe(false);

    // Verify edit mode buttons are visible
    expect(testWrapper.find('[data-testid="cancel-edit-button"]').exists()).toBe(true);
    expect(testWrapper.find('[data-testid="save-edit-button"]').exists()).toBe(true);
  });
});
