import { describe, it, expect, vi, beforeEach } from 'vitest';
import { shallowMount } from '@vue/test-utils';
import { createP<PERSON>, setActivePinia } from 'pinia';
import { ref } from 'vue';

// Mock components
vi.mock('@services/ui-component-library/components/BravoButton.vue', () => ({
  default: {
    name: 'BravoButton',
    template: '<button><slot /></button>',
    props: ['label', 'icon', 'severity', 'outlined', 'text', 'disabled'],
  },
}));

vi.mock('@services/ui-component-library/components/BravoTextarea.vue', () => ({
  default: {
    name: 'BravoTextarea',
    template: '<textarea></textarea>',
    props: ['modelValue'],
  },
}));

// Mock the FilterButton component
vi.mock('../../../src/modules/knowledge/components/FilterButton.vue', () => ({
  default: {
    name: 'FilterBtn',
    template: '<div class="filter-btn"></div>',
    emits: ['update:filters', 'popover:hide', 'popover:show'],
  },
}));

// Mock PrimeVue Toast
vi.mock('primevue/usetoast', () => {
  return {
    useToast: vi.fn(() => ({
      add: vi.fn(),
      removeGroup: vi.fn(),
      removeAllGroups: vi.fn(),
    }))
  };
});

// Mock the useKnowledgeAPI composable
const mockFetchArticleEvents = vi.fn().mockResolvedValue({
  items: [],
  totalCount: 0,
});

vi.mock('../../../src/composables/services/useKnowledgeAPI', () => ({
  useKnowledgeAPI: vi.fn(() => ({
    fetchArticleEvents: mockFetchArticleEvents,
  })),
}));

// Mock stores
vi.mock('../../../src/modules/knowledge/stores/knowledge.ts', () => ({
  useKnowledgeStore: vi.fn(() => ({
    fetchComments: vi.fn(),
    comments: ref([]),
    loadingComments: ref(false),
  })),
}));

vi.mock('../../../stores/user.ts', () => ({
  useUserStore: vi.fn(() => ({
    getPartnerLabelById: vi.fn((id) => `User ${id}`),
  })),
}));

// Mock i18n
vi.mock('vue-i18n', () => ({
  useI18n: vi.fn(() => ({
    t: (key: string) => key,
  })),
}));

// Import the component
import ArticleSidebar from '../../../src/modules/knowledge/components/ArticleSidebar.vue';

describe('ArticleSidebar.vue', () => {
  let wrapper;

  const mockArticle = {
    id: '123',
    kb_id: 'kb123',
    title: 'Test Article',
    content: 'Test content',
    root_parent_id: 'root123',
  };

  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();

    wrapper = shallowMount(ArticleSidebar, {
      props: {
        article: mockArticle,
        relatedArticles: [],
        loadingRelatedArticles: false,
        isPanelMinimized: false,
      },
      global: {
        stubs: {
          Tabs: true,
          TabList: true,
          Tab: true,
          TabPanel: true,
          BravoTimeline: true,
          ArticleInformationBlock: true,
          Skeleton: true,
          FilterBtn: {
            template: '<div class="filter-btn"></div>',
            emits: ['update:filters', 'popover:hide', 'popover:show']
          },
          BravoButton: {
            template: '<button class="refresh-button"><slot /></button>',
            props: ['icon', 'severity']
          }
        },
        directives: {
          tooltip: () => {}
        },
        mocks: {
          $primevue: {
            config: {
              ripple: false
            }
          }
        }
      },
    });
  });

  it('should pass correct filter parameters when filter changes', async () => {
    // Directly call the onFilterChange method with test filter indexes
    const testFilterIndexes = [1, 4, 5]; // Example filter indexes
    await wrapper.vm.onFilterChange(testFilterIndexes);

    // Verify that the currentFilterIndexes were updated
    expect(wrapper.vm.currentFilterIndexes).toEqual(testFilterIndexes);

    // Directly call the fetchEventsWithFilters method
    await wrapper.vm.fetchEventsWithFilters(testFilterIndexes);

    // Verify that fetchArticleEvents was called with the correct parameters
    expect(mockFetchArticleEvents).toHaveBeenCalledWith('kb123', expect.objectContaining({
      filter: expect.arrayContaining([
        { property: 'kb_id', value: 'kb123' },
        { property: 'root_kb_id', value: '_no_filter_' },
        { property: 'limit_diff_log', value: true },
        { property: 'type', operator: 'in', value: testFilterIndexes }
      ])
    }));
  });

  it('should pass default filter parameters when no filters are selected', async () => {
    // Directly call the onFilterChange method with empty array
    await wrapper.vm.onFilterChange([]);

    // Directly call the fetchEventsWithFilters method
    await wrapper.vm.fetchEventsWithFilters([]);

    // Verify that fetchArticleEvents was called with the correct parameters
    expect(mockFetchArticleEvents).toHaveBeenCalledWith('kb123', expect.objectContaining({
      filter: expect.arrayContaining([
        { property: 'kb_id', value: 'kb123' },
        { property: 'root_kb_id', value: '_no_filter_' },
        { property: 'limit_diff_log', value: true },
        { property: 'type', operator: 'ne', value: '_no_filter_' }
      ])
    }));
  });

  it('should refresh events when refresh button is clicked', async () => {
    // Set some initial filter values
    wrapper.vm.currentFilterIndexes = [1, 4, 5];

    // Directly call the refreshEvents method
    await wrapper.vm.refreshEvents();

    // Verify that fetchArticleEvents was called with the current filter indexes
    expect(mockFetchArticleEvents).toHaveBeenCalledWith('kb123', expect.objectContaining({
      filter: expect.arrayContaining([
        { property: 'kb_id', value: 'kb123' },
        { property: 'root_kb_id', value: '_no_filter_' },
        { property: 'limit_diff_log', value: true },
        { property: 'type', operator: 'in', value: [1, 4, 5] }
      ])
    }));
  });
});
