import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vueDevTools from 'vite-plugin-vue-devtools';
import packageJson from './package.json';

const branchName = process.env.BRANCH_NAME || ''
const base = branchName ? `/preview_build/${branchName}/` : './'

// https://vite.dev/config/
export default defineConfig({
  base,
  define: {
    'process.env': JSON.stringify({}),
    'development': JSON.stringify(process.env.NODE_ENV === 'development'),
    'import.meta.env.PACKAGE_VERSION': JSON.stringify(packageJson.version),
    'import.meta.env.DATADOG_APP_ID': JSON.stringify(process.env.DATADOG_APP_ID || ''),
    'import.meta.env.DATADOG_ENV': JSON.stringify(process.env.DATADOG_ENV || process.env.NODE_ENV || 'dev'),
    'import.meta.env.DATADOG_CLIENT_TOKEN': JSON.stringify(process.env.DATADOG_CLIENT_TOKEN || ''),
  },
  plugins: [
    vue({
      include: [/\.vue$/, /node_modules\/@services\/.*\.vue$/]
    }),
    vueDevTools(),
    {
      name: 'preserve-init-js-path',
      transformIndexHtml(html) {
        // Replace ./init.js with /init.js in the built index.html
        return html.replace('./init.js', '/init.js');
      },
    },
  ],
  build: {
    outDir: './dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        entryFileNames: 'assets/[name].js', // This ensures your index.js file keeps its name
        //chunkFileNames: 'assets/js/[name]-[hash].js', //Customize chunk names if needed
        assetFileNames: 'assets/[name].[ext]', // Customize asset file names if needed
      },
      external: ['axios'],
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    cors: {
      origin: [
        'https://appv5.stage-ovationcxm.com'
      ],
      credentials: true
    },
    proxy: {
      '/admin/v4': {
        target: 'https://api.stage.goboomtown.com',
        changeOrigin: true,
        secure: true,
        ws: true,
        cookieDomainRewrite: { '*': '' },
        cookiePathRewrite: { '*': '/' }
      },
      '^/journeys/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/tasksmywork/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/tasks-vite/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/connectors/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/generative-summarizer/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      }
    }
  }
})
