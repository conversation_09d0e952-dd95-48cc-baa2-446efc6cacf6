<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="./favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/init.js"></script>
    <title>OvationCXM</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="./src/main.ts"></script>
    <script>
      ; (function () {
        window.OvationMessenger = window.OvationMessenger || {}
        window.OvationMessenger.config = {
          integrationId: 'JMAC2L',
          partnerToken: 'B3C6B7890DCD078CB7F1',
          partnerTeam: '9AM-7X6',
          hidden: true,
        }

        // Add a global function to identify user that can be called after login
        window.identifyMessengerUser = function(userData) {
          if (window.OvationMessenger && typeof window.OvationMessenger.identifyUser === 'function') {
            window.OvationMessenger.identifyUser({
              email: userData.email || '',
              phone: userData.phone || '',
              firstName: userData.firstName || userData.name || '',
            });
          } else {
            console.warn('OvationMessenger not ready yet, will try again in 1 second');
            setTimeout(function() {
              window.identifyMessengerUser(userData);
            }, 1000);
          }
        }
        
        var loadMessengerConnect = function () {
          var script = document.createElement('script')
          script.type = 'text/javascript'
          script.async = true
          script.src = 'https://messenger.goboomtown.com/js/messengerConnect.js'
          document.body.appendChild(script)
        }
   
        if (
          document.readyState === 'complete' ||
          document.readyState === 'interactive'
        ) {
          loadMessengerConnect()
        } else {
          document.addEventListener(
            'DOMContentLoaded',
            loadMessengerConnect,
            false,
          )
        }
   
      })()
    </script>
  </body>
</html>
